// Generated C++ file by <PERSON><PERSON><PERSON>ppInspectorPro - http://www.djkaty.com - https://github.com/djkaty
// Modified by <PERSON><PERSON><PERSON><PERSON><PERSON> - https://github.com/jadis0x
// Target Unity version: 2021.2.0 - 2021.2.99

// ******************************************************************************
// * IL2CPP application-specific method definition addresses and signatures
// ******************************************************************************

using namespace app;

DO_APP_FUNC(0x02F89F70, String*, Application_get_version, (MethodInfo* method));
DO_APP_FUNC(0x00C95DE<PERSON>, CSteamID, SteamUser_GetSteamID, (MethodInfo* method));
DO_APP_FUNC(0x00C93890, String*, SteamFriends_GetPersonaName, (MethodInfo* method));
DO_APP_FUNC(0x02F8A010, void, Application_OpenURL, (String* url, MethodInfo* method));
DO_APP_FUNC(0x00711290, void, Menu_ShowMessageModal, (Menu* __this, String* text, MethodInfo* method));
DO_APP_FUNC(0x01C30790, String*, Object_ToString, (Object* __this, MethodInfo* method));
DO_APP_FUNC(0x03294580, void, Text_set_text, (Text* __this, String* value, MethodInfo* method));
DO_APP_FUNC(0x02FE4340, Object_1__Array*, Object_1_FindObjectsOfType, (Type* type, MethodInfo* method));
DO_APP_FUNC(0x00A072D0, Transform*, Component_get_transform, (Component* __this, MethodInfo* method));
DO_APP_FUNC(0x02FEE300, Vector3, Transform_get_forward, (Transform* __this, MethodInfo* method));
DO_APP_FUNC(0x02FEDBC0, Vector3, Transform_get_right, (Transform* __this, MethodInfo* method));
DO_APP_FUNC(0x02FEDF60, Vector3, Transform_get_up, (Transform* __this, MethodInfo* method));
DO_APP_FUNC(0x02FDCDC0, GameObject*, Component_get_gameObject, (Component* __this, MethodInfo* method));
DO_APP_FUNC(0x01F732A0, void, UltimateCharacterLocomotion_SetPosition_1, (UltimateCharacterLocomotion* __this, Vector3 position, bool snapAnimator, MethodInfo* method));
DO_APP_FUNC(0x02FDD8E0, Component*, GameObject_GetComponentByName, (GameObject* __this, String* type, MethodInfo* method));
DO_APP_FUNC(0x01F6BA60, void, UltimateCharacterLocomotion_set_TimeScale, (UltimateCharacterLocomotion* __this, float value, MethodInfo* method));
DO_APP_FUNC(0x02FDE4D0, void, GameObject_set_active, (GameObject* __this, bool value, MethodInfo* method));
DO_APP_FUNC(0x02F90CF0, void, Debug_2_Log, (Object* message, MethodInfo* method));
DO_APP_FUNC(0x02F91440, void, Debug_2_LogWarning, (Object* message, MethodInfo* method));
DO_APP_FUNC(0x00548B00, void, NolanBehaviour_OnAttributeUpdateValue, (NolanBehaviour* __this, Attribute_1* attribute, MethodInfo* method));
DO_APP_FUNC(0x006DACD0, void, Menu_Update, (Menu* __this, MethodInfo* method));
DO_APP_FUNC(0x00536F60, void, NolanBehaviour_Update, (NolanBehaviour* __this, MethodInfo* method));
DO_APP_FUNC(0x005342C0, void, NolanBehaviour_FixedUpdate, (NolanBehaviour* __this, MethodInfo* method));
DO_APP_FUNC(0x006BA030, RankHelpers_ExpGainInfo*, RankHelpers_CalculateExpGain, (RankHelpers* __this, int32_t mapProgress, int32_t numAwards, GameConfigToken* gameConfigToken, MethodInfo* method));
DO_APP_FUNC(0x006AE740, bool, OptionsHelpers_IsRobeUnlocked, (OptionsHelpers* __this, String* robe, String* character, MethodInfo* method));
DO_APP_FUNC(0x01F08DD0, bool, Cursor_1_get_visible, (MethodInfo* method));
DO_APP_FUNC(0x02FCDD10, void, Cursor_1_set_visible, (bool value, MethodInfo* method));
DO_APP_FUNC(0x02FCDD60, CursorLockMode__Enum, Cursor_1_get_lockState, (MethodInfo* method));
DO_APP_FUNC(0x02FCDDB0, void, Cursor_1_set_lockState, (CursorLockMode__Enum value, MethodInfo* method));
DO_APP_FUNC(0x0064F7B0, bool, LockedInteractable_CanInteract, (LockedInteractable* __this, GameObject* character, MethodInfo* method));
DO_APP_FUNC(0x006ACDF0, bool, OptionsHelpers_IsCharacterUnlocked, (OptionsHelpers* __this, String* prefab, MethodInfo* method));
DO_APP_FUNC(0x00643860, bool, DevourInput_GetLongPress, (DevourInput* __this, String* name, float duration, bool waitForRelease, MethodInfo* method));
DO_APP_FUNC(0x006E1890, UIPerkSelectionType*, Menu_SetupPerk, (Menu* __this, CharacterPerk* perk, MethodInfo* method));
DO_APP_FUNC(0x006E12F0, UIOutfitSelectionType*, Menu_SetupOutfit, (Menu* __this, CharacterOutfit* outfit, MethodInfo* method));
DO_APP_FUNC(0x006E2020, UIFlashlightSelectionType*, Menu_SetupFlashlight, (Menu* __this, CharacterFlashlight* flashlight, MethodInfo* method));
DO_APP_FUNC(0x006E26F0, UIEmoteSelectionType*, Menu_SetupEmote, (Menu* __this, CharacterEmote* emote, MethodInfo* method));
DO_APP_FUNC(0x005EC070, bool, SurvivalLobbyController_CanReady, (SurvivalLobbyController* __this, MethodInfo* method));
DO_APP_FUNC(0x005EC380, bool, SurvivalLobbyController_PlayableCharacterSelected, (SurvivalLobbyController* __this, MethodInfo* method));
DO_APP_FUNC(0x005EC9B0, bool, SurvivalLobbyController_UnlockedCharacterSelected, (SurvivalLobbyController* __this, MethodInfo* method));
DO_APP_FUNC(0x005EE100, bool, SurvivalLobbyController_AllPlayersReady, (SurvivalLobbyController* __this, MethodInfo* method));
DO_APP_FUNC(0x005EFB00, void, SurvivalLobbyController_OnSelectOutfit, (SurvivalLobbyController* __this, CharacterOutfit* outfit, MethodInfo* method));
DO_APP_FUNC(0x0063AB50, void, UIOutfitSelectionType_SetLocked, (UIOutfitSelectionType* __this, bool locked, MethodInfo* method));
DO_APP_FUNC(0x005B87A0, bool, SteamInventoryManager_UserHasItem, (SteamInventoryManager* __this, int32_t steamItemDefID, MethodInfo* method));
DO_APP_FUNC(0x005B6A50, bool, SteamInventoryManager_HasRetrievedUserInventoryItems, (SteamInventoryManager* __this, MethodInfo* method));
DO_APP_FUNC(0x00B120E0, int32_t, BoltNetwork_get_MaxConnections, (MethodInfo* method));
DO_APP_FUNC(0x006C61F0, void, UIHelpers_ShowMouseCursor, (MethodInfo* method));
DO_APP_FUNC(0x006C6160, void, UIHelpers_HideMouseCursor, (MethodInfo* method));
DO_APP_FUNC(0x02FDE380, Transform*, GameObject_get_transform, (GameObject* __this, MethodInfo* method));
DO_APP_FUNC(0x02FED5B0, Vector3, Transform_get_position, (Transform* __this, MethodInfo* method));
DO_APP_FUNC(0x02FEE6A0, Quaternion, Transform_get_rotation, (Transform* __this, MethodInfo* method));
DO_APP_FUNC(0x00427A10, Vector3, Quaternion_get_eulerAngles, (Quaternion* __this, MethodInfo* method));
DO_APP_FUNC(0x004B2570, Quaternion, Quaternion_Euler, (float x, float y, float z, MethodInfo* method));
DO_APP_FUNC(0x02FD5100, Quaternion, Quaternion_get_identity, (MethodInfo* method));
DO_APP_FUNC(0x006F6E90, void, Menu_OnLobbyStartButtonClick, (Menu* __this, MethodInfo* method));
DO_APP_FUNC(0x0053C010, void, NolanBehaviour_StartCarry, (NolanBehaviour* __this, String* objectName, MethodInfo* method));
DO_APP_FUNC(0x00558E10, void, MapController_SetProgressTo, (MapController* __this, int32_t progress, MethodInfo* method));
DO_APP_FUNC(0x005A6200, void, SlaughterhouseAltarController_SkipToGoat, (SlaughterhouseAltarController* __this, int32_t number, MethodInfo* method));
DO_APP_FUNC(0x005FE280, void, SurvivalObjectBurnController_SkipToGoat, (SurvivalObjectBurnController* __this, int32_t number, MethodInfo* method));
DO_APP_FUNC(0x00570560, void, NolanRankController_SetRank, (NolanRankController* __this, int32_t rank, MethodInfo* method));
DO_APP_FUNC(0x02FADD40, void, Light_set_intensity, (Light* __this, float value, MethodInfo* method));
DO_APP_FUNC(0x02FAE520, void, Light_set_range, (Light* __this, float value, MethodInfo* method));
DO_APP_FUNC(0x02FAD9B0, void, Light_set_spotAngle, (Light* __this, float value, MethodInfo* method));
DO_APP_FUNC(0x02FAE9B0, void, Light_set_shadows, (Light* __this, LightShadows__Enum value, MethodInfo* method));
DO_APP_FUNC(0x00550F50, void, NolanBehaviour_TeleportTo, (NolanBehaviour* __this, Vector3 position, Quaternion rotation, bool snapAnimator, MethodInfo* method));
DO_APP_FUNC(0x006618E0, void, SurvivalReviveInteractable_Interact, (SurvivalReviveInteractable* __this, GameObject* character, MethodInfo* method));
DO_APP_FUNC(0x0067D6A0, void, SurvivalAzazelBehaviour_OnPickedUpPlayer, (SurvivalAzazelBehaviour* __this, GameObject* ai, GameObject* player, bool inHidingSpot, MethodInfo* method));
DO_APP_FUNC(0x0067D9D0, void, SurvivalAzazelBehaviour_OnKnockout, (SurvivalAzazelBehaviour* __this, GameObject* ai, GameObject* player, MethodInfo* method));
DO_APP_FUNC(0x02F8DEE0, Camera*, Camera_get_main, (MethodInfo* method));
DO_APP_FUNC(0x02F8D6A0, Vector3, Camera_WorldToScreenPoint_1, (Camera* __this, Vector3 position, MethodInfo* method));
DO_APP_FUNC(0x00B21C60, bool, Behaviour_get_enabled, (Behaviour* __this, MethodInfo* method));
DO_APP_FUNC(0x02FE4880, Object_1*, Object_1_FindObjectOfType, (Type* type, MethodInfo* method));
DO_APP_FUNC(0x01E1F580, void, Behaviour_set_enabled, (Behaviour* __this, bool value, MethodInfo* method));
DO_APP_FUNC(0x02FDDBA0, Component__Array*, GameObject_GetComponents, (GameObject* __this, Type* type, MethodInfo* method));
DO_APP_FUNC(0x00B11FA0, bool, BoltNetwork_get_IsSinglePlayer, (MethodInfo* method));
DO_APP_FUNC(0x00B11F50, bool, BoltNetwork_get_IsServer, (MethodInfo* method));
DO_APP_FUNC(0x00B21930, BoltEntity*, EntityBehaviour_get_entity, (EntityBehaviour* __this, MethodInfo* method));
DO_APP_FUNC(0x00B1A3B0, bool, BoltEntity_get_IsAttached, (BoltEntity* __this, MethodInfo* method));
DO_APP_FUNC(0x00B1A550, bool, BoltEntity_get_IsOwner, (BoltEntity* __this, MethodInfo* method));
DO_APP_FUNC(0x0054D050, bool, NolanBehaviour_IsCrawling, (NolanBehaviour* __this, MethodInfo* method));
DO_APP_FUNC(0x005D0810, bool, Survival_IsEndingPlaying, (Survival* __this, MethodInfo* method));
DO_APP_FUNC(0x005D0820, bool, Survival_IsJumpScarePlaying, (Survival* __this, MethodInfo* method));
DO_APP_FUNC(0x005D0980, bool, Survival_StartingToPlayFailEnding, (Survival* __this, MethodInfo* method));
DO_APP_FUNC(0x005C83B0, GameObject*, Survival_GetAzazel, (Survival* __this, MethodInfo* method));
DO_APP_FUNC(0x006BE440, SaveHelpers*, SaveHelpers_get_singleton, (MethodInfo* method));
DO_APP_FUNC(0x00696E10, InGameHelpers*, InGameHelpers_get_singleton, (MethodInfo* method));
DO_APP_FUNC(0x0069B350, String*, InGameHelpers_GetAzazelName, (InGameHelpers* __this, MethodInfo* method));
DO_APP_FUNC(0x02FE8E70, float, Time_1_get_deltaTime, (MethodInfo* method));
DO_APP_FUNC(0x0304B710, float, Input_1_GetAxis, (String* axisName, MethodInfo* method));
DO_APP_FUNC(0x02FD4680, String*, Vector3_ToString, (Vector3* __this, MethodInfo* method));
DO_APP_FUNC(0x02FE53A0, String*, Object_1_GetName, (Object_1* obj, MethodInfo* method));
DO_APP_FUNC(0x02FDE8A0, GameObject__Array*, GameObject_FindGameObjectsWithTag, (String* tag, MethodInfo* method));
DO_APP_FUNC(0x02FE30B0, bool, Object_1_op_Implicit, (Object_1* exists, MethodInfo* method));

