// 简单的编译测试文件
#include "framework/pch-il2cpp.h"

int main() {
    // 测试一些基本的 IL2CPP 类型是否可用
    Il2CppClass* testClass = nullptr;
    Il2CppObject* testObject = nullptr;
    MethodInfo* testMethod = nullptr;

    // 测试一些 app 命名空间的类型
    using namespace app;
    String* testString = nullptr;
    GameObject* testGameObject = nullptr;
    Transform* testTransform = nullptr;
    Vector3 testVector = {0.0f, 0.0f, 0.0f};

    // 测试一些函数指针是否可用
    if (Application_get_version != nullptr) {
        // 函数指针存在
    }

    return 0;
}
