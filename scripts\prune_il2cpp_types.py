#!/usr/bin/env python3
"""
prune_il2cpp_types.py
---------------------
根据项目实际使用情况，对 Unity IL2CPP 自动生成的巨大头文件 `appdata/il2cpp-types.h` 进行裁剪，
只保留源码中真正引用到的类型定义，以加快 IntelliSense / 编译速度。

工作流程：
1. 遍历指定源代码目录（默认排除 appdata/ 与 include/ 这种第三方或生成目录）。
2. 正则提取所有出现的 `XXX__TypeInfo` 以获取被使用的类型名前缀 `XXX`。
3. 逐行扫描 `il2cpp-types.h`，将与目标类型相关的声明/定义复制到输出文件，
   包括：
   - forward declaration（例如 `struct XXX;`、`struct XXX__Class;` 等）
   - 结构体完整定义 `struct XXX { ... }`、`struct XXX__Class { ... }`、`struct XXX__StaticFields { ... }` 等
   - 变量声明 `extern XXX__Class** XXX__TypeInfo;` 等
4. 同时保留文件头部公共声明（MonitorData、Il2CppArrayBounds 等），以确保编译完整。

使用示例：
$ python scripts/prune_il2cpp_types.py \
    --input appdata/il2cpp-types.h \
    --output appdata/il2cpp-types.trimmed.h
"""
import argparse
import os
import re
import sys
from pathlib import Path

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def collect_used_types(search_paths, exclude_dirs=None):
    """搜索项目中使用的所有 IL2CPP 类型。"""
    # 多种模式来识别使用的类型
    patterns = [
        re.compile(r"(\w+)__TypeInfo"),  # 原始模式
        re.compile(r"app::(\w+)"),       # app:: 命名空间类型
        re.compile(r"struct\s+(\w+)\s*\*"),  # 结构体指针
        re.compile(r"(\w+)\s*\*\s*\w+"),     # 类型指针变量
    ]

    used_types = set()
    exclude_dirs = set(exclude_dirs or [])
    exts = {".h", ".hpp", ".cpp", ".c"}

    for root_path in search_paths:
        for root, dirs, files in os.walk(root_path):
            # 跳过排除目录
            if any(Path(root).match(str(Path(root_path) / ex)) for ex in exclude_dirs):
                continue
            for f in files:
                if Path(f).suffix.lower() not in exts:
                    continue
                try:
                    with open(os.path.join(root, f), "r", encoding="utf-8", errors="ignore") as fh:
                        content = fh.read()
                        for pattern in patterns:
                            for m in pattern.finditer(content):
                                type_name = m.group(1)
                                # 过滤掉一些常见的非IL2CPP类型
                                if not type_name.lower() in {'int', 'char', 'void', 'bool', 'float', 'double', 'size_t', 'uint32_t', 'int32_t', 'uint64_t', 'int64_t', 'uint16_t', 'int16_t', 'uint8_t', 'int8_t', 'uintptr_t', 'intptr_t'}:
                                    used_types.add(type_name)
                except Exception:
                    # 忽略无法读取的文件
                    continue

    print(f"发现 {len(used_types)} 个可能使用的类型")
    return used_types


def prune_header(src_path: Path, dst_path: Path, used_types):
    """读取巨大头文件，提取使用到的类型声明与定义。"""
    # 正则：捕获 struct 定义起始行。例如："struct BoltPrefabs {" or "struct BoltPrefabs__Class {"
    struct_begin_pattern = re.compile(r"^struct\s+(\w+)\s*{?")

    # 因为定义块可能嵌套，简单起见，使用花括号计数法复制完整 struct 定义。
    with src_path.open("r", encoding="utf-8", errors="ignore") as src, \
            dst_path.open("w", encoding="utf-8") as dst:

        dst.write("// Auto-generated by prune_il2cpp_types.py. 仅保留项目实际使用的类型。\n\n")

        copy_always = True  # 前导公共区域直接复制直到第一次遇到 struct XXX
        copying_block = False
        brace_depth = 0
        current_block_keep = False

        for line in src:
            if copy_always:
                # 检测到第一个 struct 后取消 always 模式
                m = struct_begin_pattern.match(line)
                if m:
                    copy_always = False
                    # 继续 fallthrough 让后续逻辑处理
                else:
                    dst.write(line)
                    continue

            # 处理 struct 块复制逻辑
            if not copying_block:
                m = struct_begin_pattern.match(line)
                if m:
                    struct_name = m.group(1)
                    base_name = struct_name.split("__")[0]  # 提取 "XXX" 前缀
                    current_block_keep = base_name in used_types
                    if current_block_keep:
                        copying_block = True
                        brace_depth = 0
                        dst.write(line)
                        brace_depth += line.count("{") - line.count("}")
                    # else: 跳过整个定义块
                else:
                    # 非 struct 定义行，可能是 extern 前向声明等
                    # 始终保留简单前向声明，避免缺失依赖类型
                    simple_forward_decl = re.compile(r"^\s*struct\s+\w+(?:__\w+)?\s*;\s*$")
                    if simple_forward_decl.match(line):
                        dst.write(line)
                        continue

                    extern_pattern = re.compile(r"^\s*extern\s+.*?(\w+)__TypeInfo\s*;")
                    em = extern_pattern.match(line)
                    if em:
                        base_name = em.group(1)
                        if base_name in used_types:
                            dst.write(line)
                    # 其他行忽略
                continue
            else:
                # 仍在复制 struct 定义块
                dst.write(line) if current_block_keep else None
                brace_depth += line.count("{") - line.count("}")
                if brace_depth <= 0:
                    copying_block = False
        
    print(f"[OK] 写入精简头文件 -> {dst_path}，保留类型数量: {len(used_types)}")


# ---------------------------------------------------------------------------
# Main
# ---------------------------------------------------------------------------

def main():
    parser = argparse.ArgumentParser(description="Prune unused types from il2cpp-types.h")
    parser.add_argument("--input", default="appdata/il2cpp-types.h", help="Path to original il2cpp-types.h")
    parser.add_argument("--output", default="appdata/il2cpp-types.trimmed.h", help="Path for trimmed header")
    parser.add_argument("--source-dirs", nargs="*", default=["."], help="Directories to scan for C/C++ sources")
    parser.add_argument("--exclude-dirs", nargs="*", default=["appdata", "include", "lib/private", ".git", "x64", ".vs"], help="Directories to ignore during scanning")

    args = parser.parse_args()

    used_types = collect_used_types(args.source_dirs, exclude_dirs=args.exclude_dirs)
    if not used_types:
        print("未检测到任何 __TypeInfo 使用，脚本终止。")
        sys.exit(1)

    prune_header(Path(args.input), Path(args.output), used_types)


if __name__ == "__main__":
    main() 