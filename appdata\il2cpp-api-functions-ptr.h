// Generated C++ file by Il2CppInspectorPro - http://www.djkaty.com - https://github.com/djkaty
// Modified by Jadis0x - https://github.com/jadis0x
// Target Unity version: 2021.2.0 - 2021.2.99

// ******************************************************************************
// * IL2CPP API function pointers
// ******************************************************************************

#define il2cpp_add_internal_call_ptr 0x0036F5A0
#define il2cpp_alloc_ptr 0x00348C70
#define il2cpp_allocation_granularity_ptr 0x0022C7E0
#define il2cpp_array_class_get_ptr 0x0036F5C0
#define il2cpp_array_element_size_ptr 0x0036F680
#define il2cpp_array_get_byte_length_ptr 0x0036F5D0
#define il2cpp_array_length_ptr 0x000047A0
#define il2cpp_array_new_ptr 0x0036F620
#define il2cpp_array_new_full_ptr 0x0036F660
#define il2cpp_array_new_specific_ptr 0x0036F650
#define il2cpp_array_object_header_size_ptr 0x002541A0
#define il2cpp_assembly_get_image_ptr 0x00021670
#define il2cpp_bounded_array_class_get_ptr 0x0036F670
#define il2cpp_capture_memory_snapshot_ptr 0x003718D0
#define il2cpp_class_array_element_size_ptr 0x0036FB30
#define il2cpp_class_enum_basetype_ptr 0x0036F690
#define il2cpp_class_for_each_ptr 0x00371ED0
#define il2cpp_class_from_il2cpp_type_ptr 0x0036F770
#define il2cpp_class_from_name_ptr 0x0036F780
#define il2cpp_class_from_system_type_ptr 0x0036F6B0
#define il2cpp_class_from_type_ptr 0x0036F770
#define il2cpp_class_get_assemblyname_ptr 0x0036FBF0
#define il2cpp_class_get_bitmap_ptr 0x0036FC40
#define il2cpp_class_get_bitmap_size_ptr 0x0036FC20
#define il2cpp_class_get_data_size_ptr 0x0036FC10
#define il2cpp_class_get_declaring_type_ptr 0x002785A0
#define il2cpp_class_get_element_class_ptr 0x00238F90
#define il2cpp_class_get_events_ptr 0x0036F790
#define il2cpp_class_get_field_from_name_ptr 0x0036F9F0
#define il2cpp_class_get_fields_ptr 0x0036F7A0
#define il2cpp_class_get_flags_ptr 0x0036FAF0
#define il2cpp_class_get_image_ptr 0x00021670
#define il2cpp_class_get_interfaces_ptr 0x0036F830
#define il2cpp_class_get_method_from_name_ptr 0x0036FA90
#define il2cpp_class_get_methods_ptr 0x0036FA00
#define il2cpp_class_get_name_ptr 0x00022560
#define il2cpp_class_get_namespace_ptr 0x00025E60
#define il2cpp_class_get_nested_types_ptr 0x0036F820
#define il2cpp_class_get_parent_ptr 0x00278610
#define il2cpp_class_get_properties_ptr 0x0036F8C0
#define il2cpp_class_get_property_from_name_ptr 0x0036F8D0
#define il2cpp_class_get_rank_ptr 0x0036FC00
#define il2cpp_class_get_static_field_data_ptr 0x00003DB0
#define il2cpp_class_get_type_ptr 0x00289670
#define il2cpp_class_get_type_token_ptr 0x0036FB40
#define il2cpp_class_get_userdata_offset_ptr 0x00371EC0
#define il2cpp_class_has_attribute_ptr 0x0036FB50
#define il2cpp_class_has_parent_ptr 0x0036F710
#define il2cpp_class_has_references_ptr 0x0036FBA0
#define il2cpp_class_instance_size_ptr 0x0036FAA0
#define il2cpp_class_is_abstract_ptr 0x0036FB00
#define il2cpp_class_is_assignable_from_ptr 0x00317130
#define il2cpp_class_is_blittable_ptr 0x0036FAD0
#define il2cpp_class_is_enum_ptr 0x0036FBE0
#define il2cpp_class_is_generic_ptr 0x0036F6E0
#define il2cpp_class_is_inflated_ptr 0x0036F6F0
#define il2cpp_class_is_interface_ptr 0x0036FB10
#define il2cpp_class_is_subclass_of_ptr 0x0036F700
#define il2cpp_class_is_valuetype_ptr 0x0036FAC0
#define il2cpp_class_num_fields_ptr 0x0036FAB0
#define il2cpp_class_set_userdata_ptr 0x00371EB0
#define il2cpp_class_value_size_ptr 0x0036FAE0
#define il2cpp_current_thread_get_frame_at_ptr 0x00371460
#define il2cpp_current_thread_get_stack_depth_ptr 0x00371570
#define il2cpp_current_thread_get_top_frame_ptr 0x00371360
#define il2cpp_current_thread_walk_frame_stack_ptr 0x00371210
#define il2cpp_custom_attrs_construct_ptr 0x00371D20
#define il2cpp_custom_attrs_free_ptr 0x00003390
#define il2cpp_custom_attrs_from_class_ptr 0x00371A90
#define il2cpp_custom_attrs_from_method_ptr 0x00371AF0
#define il2cpp_custom_attrs_get_attr_ptr 0x00371BE0
#define il2cpp_custom_attrs_has_attr_ptr 0x00371B60
#define il2cpp_debug_get_method_info_ptr 0x003719F0
#define il2cpp_debugger_set_agent_options_ptr 0x00003390
#define il2cpp_domain_assembly_open_ptr 0x00370170
#define il2cpp_domain_get_ptr 0x00370160
#define il2cpp_domain_get_assemblies_ptr 0x00370180
#define il2cpp_exception_from_name_msg_ptr 0x003701B0
#define il2cpp_field_get_flags_ptr 0x00370460
#define il2cpp_field_get_name_ptr 0x00021670
#define il2cpp_field_get_offset_ptr 0x00370470
#define il2cpp_field_get_parent_ptr 0x00022560
#define il2cpp_field_get_type_ptr 0x00003C30
#define il2cpp_field_get_value_ptr 0x00370480
#define il2cpp_field_get_value_object_ptr 0x0035E890
#define il2cpp_field_has_attribute_ptr 0x00370490
#define il2cpp_field_is_literal_ptr 0x00370560
#define il2cpp_field_set_value_ptr 0x003704E0
#define il2cpp_field_set_value_object_ptr 0x003704F0
#define il2cpp_field_static_get_value_ptr 0x00370550
#define il2cpp_field_static_set_value_ptr 0x0035EFA0
#define il2cpp_format_exception_ptr 0x003701C0
#define il2cpp_format_stack_trace_ptr 0x00370250
#define il2cpp_free_ptr 0x0030E8E0
#define il2cpp_free_captured_memory_snapshot_ptr 0x003718E0
#define il2cpp_gc_alloc_fixed_ptr 0x00370770
#define il2cpp_gc_collect_ptr 0x00370570
#define il2cpp_gc_collect_a_little_ptr 0x00370590
#define il2cpp_gc_disable_ptr 0x002EDF80
#define il2cpp_gc_enable_ptr 0x002EDF30
#define il2cpp_gc_foreach_heap_ptr 0x003706E0
#define il2cpp_gc_free_fixed_ptr 0x0036BBF0
#define il2cpp_gc_get_heap_size_ptr 0x003706D0
#define il2cpp_gc_get_max_time_slice_ns_ptr 0x003706A0
#define il2cpp_gc_get_used_size_ptr 0x003706C0
#define il2cpp_gc_has_strict_wbarriers_ptr 0x001069E0
#define il2cpp_gc_is_disabled_ptr 0x003705C0
#define il2cpp_gc_is_incremental_ptr 0x00370690
#define il2cpp_gc_set_external_allocation_tracker_ptr 0x00003390
#define il2cpp_gc_set_external_wbarrier_tracker_ptr 0x00003390
#define il2cpp_gc_set_max_time_slice_ns_ptr 0x003706B0
#define il2cpp_gc_set_mode_ptr 0x003705D0
#define il2cpp_gc_start_incremental_collection_ptr 0x003705A0
#define il2cpp_gc_wbarrier_set_field_ptr 0x00370810
#define il2cpp_gchandle_foreach_get_target_ptr 0x003707E0
#define il2cpp_gchandle_free_ptr 0x00370870
#define il2cpp_gchandle_get_target_ptr 0x003707D0
#define il2cpp_gchandle_new_ptr 0x00370780
#define il2cpp_gchandle_new_weakref_ptr 0x003707A0
#define il2cpp_get_corlib_ptr 0x0036F590
#define il2cpp_get_exception_argument_null_ptr 0x003169F0
#define il2cpp_image_get_assembly_ptr 0x00022560
#define il2cpp_image_get_class_ptr 0x00371890
#define il2cpp_image_get_class_count_ptr 0x000047A0
#define il2cpp_image_get_entry_point_ptr 0x00371870
#define il2cpp_image_get_filename_ptr 0x00021670
#define il2cpp_image_get_name_ptr 0x00021670
#define il2cpp_init_ptr 0x0036F1D0
#define il2cpp_init_utf16_ptr 0x0036F200
#define il2cpp_is_debugger_attached_ptr 0x003719D0
#define il2cpp_is_vm_thread_ptr 0x00371200
#define il2cpp_method_get_class_ptr 0x000421F0
#define il2cpp_method_get_declaring_type_ptr 0x000421F0
#define il2cpp_method_get_flags_ptr 0x00370B70
#define il2cpp_method_get_from_reflection_ptr 0x00022560
#define il2cpp_method_get_name_ptr 0x00025E60
#define il2cpp_method_get_object_ptr 0x00370AB0
#define il2cpp_method_get_param_ptr 0x00370B00
#define il2cpp_method_get_param_count_ptr 0x00370AF0
#define il2cpp_method_get_param_name_ptr 0x00370B80
#define il2cpp_method_get_return_type_ptr 0x00042710
#define il2cpp_method_get_token_ptr 0x0027C790
#define il2cpp_method_has_attribute_ptr 0x00370B20
#define il2cpp_method_is_generic_ptr 0x00370AC0
#define il2cpp_method_is_inflated_ptr 0x00370AD0
#define il2cpp_method_is_instance_ptr 0x00370AE0
#define il2cpp_monitor_enter_ptr 0x00370F00
#define il2cpp_monitor_exit_ptr 0x00370F20
#define il2cpp_monitor_pulse_ptr 0x00370F30
#define il2cpp_monitor_pulse_all_ptr 0x00370F40
#define il2cpp_monitor_try_enter_ptr 0x00370F10
#define il2cpp_monitor_try_wait_ptr 0x00370FA0
#define il2cpp_monitor_wait_ptr 0x00370F90
#define il2cpp_native_stack_trace_ptr 0x00370370
#define il2cpp_object_get_class_ptr 0x00021670
#define il2cpp_object_get_size_ptr 0x00370E70
#define il2cpp_object_get_virtual_method_ptr 0x00370ED0
#define il2cpp_object_header_size_ptr 0x0022C7E0
#define il2cpp_object_new_ptr 0x00370EE0
#define il2cpp_object_unbox_ptr 0x00287160
#define il2cpp_offset_of_array_bounds_in_array_object_header_ptr 0x0022C7E0
#define il2cpp_offset_of_array_length_in_array_object_header_ptr 0x00370880
#define il2cpp_override_stack_backtrace_ptr 0x00371670
#define il2cpp_profiler_install_ptr 0x00370BE0
#define il2cpp_profiler_install_allocation_ptr 0x00370DB0
#define il2cpp_profiler_install_enter_leave_ptr 0x00370D70
#define il2cpp_profiler_install_fileio_ptr 0x00370E10
#define il2cpp_profiler_install_gc_ptr 0x00370DD0
#define il2cpp_profiler_install_thread_ptr 0x00370E30
#define il2cpp_profiler_set_events_ptr 0x00370D10
#define il2cpp_property_get_flags_ptr 0x00090230
#define il2cpp_property_get_get_method_ptr 0x00022560
#define il2cpp_property_get_name_ptr 0x00003C30
#define il2cpp_property_get_parent_ptr 0x00021670
#define il2cpp_property_get_set_method_ptr 0x00025E60
#define il2cpp_raise_exception_ptr 0x003701A0
#define il2cpp_register_debugger_agent_transport_ptr 0x00003390
#define il2cpp_register_log_callback_ptr 0x003719C0
#define il2cpp_resolve_icall_ptr 0x0036F5B0
#define il2cpp_runtime_class_init_ptr 0x00370FD0
#define il2cpp_runtime_invoke_ptr 0x00370FC0
#define il2cpp_runtime_invoke_convert_args_ptr 0x00370FB0
#define il2cpp_runtime_object_init_ptr 0x00370FE0
#define il2cpp_runtime_object_init_exception_ptr 0x00370FF0
#define il2cpp_runtime_unhandled_exception_policy_set_ptr 0x00003390
#define il2cpp_set_commandline_arguments_ptr 0x0036F390
#define il2cpp_set_commandline_arguments_utf16_ptr 0x0036F3A0
#define il2cpp_set_config_ptr 0x0036F4D0
#define il2cpp_set_config_dir_ptr 0x0036F300
#define il2cpp_set_config_utf16_ptr 0x0036F400
#define il2cpp_set_data_dir_ptr 0x0036F330
#define il2cpp_set_default_thread_affinity_ptr 0x00003390
#define il2cpp_set_find_plugin_callback_ptr 0x003719B0
#define il2cpp_set_memory_callbacks_ptr 0x0036F560
#define il2cpp_set_temp_dir_ptr 0x0036F360
#define il2cpp_shutdown_ptr 0x0036F2F0
#define il2cpp_start_gc_world_ptr 0x00370740
#define il2cpp_stats_dump_to_file_ptr 0x0036FC90
#define il2cpp_stats_get_value_ptr 0x003700D0
#define il2cpp_stop_gc_world_ptr 0x00370710
#define il2cpp_string_chars_ptr 0x0027BCA0
#define il2cpp_string_intern_ptr 0x00371080
#define il2cpp_string_is_interned_ptr 0x00371090
#define il2cpp_string_length_ptr 0x000368F0
#define il2cpp_string_new_ptr 0x00371000
#define il2cpp_string_new_len_ptr 0x00371070
#define il2cpp_string_new_utf16_ptr 0x00371020
#define il2cpp_string_new_wrapper_ptr 0x00371000
#define il2cpp_thread_attach_ptr 0x003711B0
#define il2cpp_thread_current_ptr 0x00371180
#define il2cpp_thread_detach_ptr 0x003711C0
#define il2cpp_thread_get_all_attached_threads_ptr 0x003711E0
#define il2cpp_thread_get_frame_at_ptr 0x00371470
#define il2cpp_thread_get_stack_depth_ptr 0x00371590
#define il2cpp_thread_get_top_frame_ptr 0x00371370
#define il2cpp_thread_walk_frame_stack_ptr 0x00371270
#define il2cpp_type_equals_ptr 0x00371830
#define il2cpp_type_get_assembly_qualified_name_ptr 0x00371770
#define il2cpp_type_get_attrs_ptr 0x00371820
#define il2cpp_type_get_class_or_element_class_ptr 0x003716A0
#define il2cpp_type_get_name_ptr 0x003716D0
#define il2cpp_type_get_name_chunked_ptr 0x00371E90
#define il2cpp_type_get_object_ptr 0x00371680
#define il2cpp_type_get_type_ptr 0x00371690
#define il2cpp_type_is_byref_ptr 0x00371810
#define il2cpp_type_is_pointer_type_ptr 0x00371860
#define il2cpp_type_is_static_ptr 0x00371850
#define il2cpp_unhandled_exception_ptr 0x00370360
#define il2cpp_unity_install_unitytls_interface_ptr 0x00371A80
#define il2cpp_unity_liveness_allocate_struct_ptr 0x00370890
#define il2cpp_unity_liveness_calculation_from_root_ptr 0x00370940
#define il2cpp_unity_liveness_calculation_from_statics_ptr 0x003709A0
#define il2cpp_unity_liveness_finalize_ptr 0x003709B0
#define il2cpp_unity_liveness_free_struct_ptr 0x00370A70
#define il2cpp_unity_set_android_network_up_state_func_ptr 0x00003390
#define il2cpp_value_box_ptr 0x00316950
