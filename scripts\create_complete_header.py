#!/usr/bin/env python3
"""
create_complete_header.py
-----------------------
创建一个完整且高效的精简版头文件，通过同时使用：
1. 保留文件前N行（包含基础定义）
2. 提取项目中实际使用的类型定义
3. 保留所有前向声明
"""
import argparse
import os
import re
from pathlib import Path
import sys

def collect_used_types(search_paths, exclude_dirs=None):
    """在给定目录列表中搜索 `XXX__TypeInfo` 并返回使用到的类型集合。"""
    type_pattern = re.compile(r"(\w+)__TypeInfo")
    used_types = set()

    exclude_dirs = set(exclude_dirs or [])
    exts = {".h", ".hpp", ".cpp", ".c"}

    for root_path in search_paths:
        for root, dirs, files in os.walk(root_path):
            # 跳过排除目录
            if any(Path(root).match(str(Path(ex))) for ex in exclude_dirs):
                continue
            for f in files:
                if Path(f).suffix.lower() not in exts:
                    continue
                try:
                    with open(os.path.join(root, f), "r", encoding="utf-8", errors="ignore") as fh:
                        for line in fh:
                            for m in type_pattern.finditer(line):
                                used_types.add(m.group(1))
                except Exception:
                    # 忽略无法读取的文件
                    continue
    return used_types

def create_complete_header(input_path, output_path, common_lines=2000, search_paths=None, exclude_dirs=None):
    """创建完整的精简版头文件"""
    # 1. 获取项目中使用的类型
    used_types = collect_used_types(search_paths or ["."], exclude_dirs)
    print(f"[INFO] 项目中使用的类型数: {len(used_types)}")
    print(f"[INFO] 检测到的类型: {', '.join(list(used_types)[:10])}{'...' if len(used_types) > 10 else ''}")
    
    # 2. 处理头文件
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 正则：捕获 struct 定义起始行
    struct_begin_pattern = re.compile(r"^struct\s+(\w+)(?:__.*?)?\s*{?")
    
    with open(input_path, "r", encoding="utf-8", errors="ignore") as src, \
         open(output_path, "w", encoding="utf-8") as dst:
        
        # 写入文件头
        dst.write("// 优化版 il2cpp-types.h - 保留公共定义与项目使用的类型\n")
        dst.write("// 由 create_complete_header.py 自动生成\n\n")
        dst.write("#pragma once\n\n")
        
        # 计数器
        line_count = 0
        copied_structs = 0
        
        # 前导区域直接复制
        header_lines = []
        for _ in range(common_lines):
            try:
                line = next(src)
                header_lines.append(line)
                line_count += 1
            except StopIteration:
                break
        
        # 写入前导区域
        dst.write("".join(header_lines))
        
        # 处理剩余文件
        copying_block = False
        brace_depth = 0
        current_block_keep = False
        current_type_name = ""
        
        for line in src:
            line_count += 1
            
            # 如果不在复制块中，检查是否需要开始新块
            if not copying_block:
                # 检查是否是结构体定义开始
                m = struct_begin_pattern.match(line)
                if m:
                    struct_name = m.group(1)
                    base_name = struct_name.split("__")[0] if "__" in struct_name else struct_name
                    current_type_name = base_name
                    
                    # 如果是项目使用的类型或者前向声明，保留它
                    if base_name in used_types:
                        copying_block = True
                        brace_depth = line.count("{") - line.count("}")
                        current_block_keep = True
                        dst.write(line)
                        copied_structs += 1
                    # 否则跳过此块
                    else:
                        copying_block = "{" in line
                        brace_depth = line.count("{") - line.count("}")
                        current_block_keep = False
                else:
                    # 保留所有前向声明和其他非结构体定义行
                    # 这些通常较短，对性能影响小，但对完整性很重要
                    dst.write(line)
            else:
                # 在复制块中，根据需要写入或跳过
                if current_block_keep:
                    dst.write(line)
                
                # 更新括号深度，检查是否结束块
                brace_depth += line.count("{") - line.count("}")
                if brace_depth <= 0:
                    copying_block = False
    
    print(f"[完成] 处理了 {line_count} 行，复制了 {copied_structs} 个结构体定义")
    print(f"[完成] 优化版头文件已写入: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="创建优化版il2cpp-types.h头文件")
    parser.add_argument("--input", default="appdata/il2cpp-types.h", help="原始il2cpp-types.h路径")
    parser.add_argument("--output", default="appdata/il2cpp-types.optimized.h", help="优化版输出路径")
    parser.add_argument("--common-lines", type=int, default=2000, help="保留的基础定义行数")
    parser.add_argument("--source-dirs", nargs="*", default=["."], help="要扫描的源码目录")
    parser.add_argument("--exclude-dirs", nargs="*", default=["appdata", "include", ".git", "x64", ".vs"], help="扫描时排除的目录")
    
    args = parser.parse_args()
    create_complete_header(
        args.input, 
        args.output, 
        args.common_lines,
        args.source_dirs,
        args.exclude_dirs
    )

if __name__ == "__main__":
    main() 