// Auto-generated by prune_il2cpp_types.py. 仅保留项目实际使用的类型。

// Generated C++ file by Il2CppInspectorPro - http://www.djkaty.com - https://github.com/djkaty
// Modified by J<PERSON>s0x - https://github.com/jadis0x
// Target Unity version: 2021.2.0 - 2021.2.99

#define IS_LIBCLANG_DECOMPILER (defined(_IDACLANG_) || defined(_BINARYNINJA_))
#define IS_DECOMPILER (defined(_GHIDRA_) || defined(_IDA_) || IS_LIBCLANG_DECOMPILER)

#if defined(_GHIDRA_) || defined(_IDA_)
typedef unsigned __int8 uint8_t;
typedef unsigned __int16 uint16_t;
typedef unsigned __int32 uint32_t;
typedef unsigned __int64 uint64_t;
typedef __int8 int8_t;
typedef __int16 int16_t;
typedef __int32 int32_t;
typedef __int64 int64_t;
#endif

#if IS_LIBCLANG_DECOMPILER
typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;
typedef unsigned long uint64_t;
typedef char int8_t;
typedef short int16_t;
typedef int int32_t;
typedef long int64_t;
#endif

#if defined(_GHIDRA_) || IS_LIBCLANG_DECOMPILER
typedef int64_t intptr_t;
typedef uint64_t uintptr_t;
typedef uint64_t size_t;
#endif

#if !IS_DECOMPILER
#define _CPLUSPLUS_
#endif
// ******************************************************************************
// * IL2CPP internal types
// ******************************************************************************

typedef struct Il2CppClass Il2CppClass;
typedef struct Il2CppType Il2CppType;
typedef struct EventInfo EventInfo;
typedef struct MethodInfo MethodInfo;
typedef struct FieldInfo FieldInfo;
typedef struct PropertyInfo PropertyInfo;
typedef struct Il2CppAssembly Il2CppAssembly;
typedef struct Il2CppArray Il2CppArray;
typedef struct Il2CppDelegate Il2CppDelegate;
typedef struct Il2CppDomain Il2CppDomain;
typedef struct Il2CppImage Il2CppImage;
typedef struct Il2CppException Il2CppException;
typedef struct Il2CppProfiler Il2CppProfiler;
typedef struct Il2CppObject Il2CppObject;
typedef struct Il2CppReflectionMethod Il2CppReflectionMethod;
typedef struct Il2CppReflectionType Il2CppReflectionType;
typedef struct Il2CppString Il2CppString;
typedef struct Il2CppThread Il2CppThread;
typedef struct Il2CppAsyncResult Il2CppAsyncResult;
typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppCustomAttrInfo Il2CppCustomAttrInfo;
typedef enum
{
    IL2CPP_PROFILE_NONE = 0,
    IL2CPP_PROFILE_APPDOMAIN_EVENTS = 1 << 0,
    IL2CPP_PROFILE_ASSEMBLY_EVENTS = 1 << 1,
    IL2CPP_PROFILE_MODULE_EVENTS = 1 << 2,
    IL2CPP_PROFILE_CLASS_EVENTS = 1 << 3,
    IL2CPP_PROFILE_JIT_COMPILATION = 1 << 4,
    IL2CPP_PROFILE_INLINING = 1 << 5,
    IL2CPP_PROFILE_EXCEPTIONS = 1 << 6,
    IL2CPP_PROFILE_ALLOCATIONS = 1 << 7,
    IL2CPP_PROFILE_GC = 1 << 8,
    IL2CPP_PROFILE_THREADS = 1 << 9,
    IL2CPP_PROFILE_REMOTING = 1 << 10,
    IL2CPP_PROFILE_TRANSITIONS = 1 << 11,
    IL2CPP_PROFILE_ENTER_LEAVE = 1 << 12,
    IL2CPP_PROFILE_COVERAGE = 1 << 13,
    IL2CPP_PROFILE_INS_COVERAGE = 1 << 14,
    IL2CPP_PROFILE_STATISTICAL = 1 << 15,
    IL2CPP_PROFILE_METHOD_EVENTS = 1 << 16,
    IL2CPP_PROFILE_MONITOR_EVENTS = 1 << 17,
    IL2CPP_PROFILE_IOMAP_EVENTS = 1 << 18,
    IL2CPP_PROFILE_GC_MOVES = 1 << 19,
    IL2CPP_PROFILE_FILEIO = 1 << 20
} Il2CppProfileFlags;
typedef enum
{
    IL2CPP_PROFILE_FILEIO_WRITE = 0,
    IL2CPP_PROFILE_FILEIO_READ
} Il2CppProfileFileIOKind;
typedef enum
{
    IL2CPP_GC_EVENT_START,
    IL2CPP_GC_EVENT_MARK_START,
    IL2CPP_GC_EVENT_MARK_END,
    IL2CPP_GC_EVENT_RECLAIM_START,
    IL2CPP_GC_EVENT_RECLAIM_END,
    IL2CPP_GC_EVENT_END,
    IL2CPP_GC_EVENT_PRE_STOP_WORLD,
    IL2CPP_GC_EVENT_POST_STOP_WORLD,
    IL2CPP_GC_EVENT_PRE_START_WORLD,
    IL2CPP_GC_EVENT_POST_START_WORLD
} Il2CppGCEvent;
typedef enum
{
    IL2CPP_GC_MODE_DISABLED = 0,
    IL2CPP_GC_MODE_ENABLED = 1,
    IL2CPP_GC_MODE_MANUAL = 2
} Il2CppGCMode;
typedef enum
{
    IL2CPP_STAT_NEW_OBJECT_COUNT,
    IL2CPP_STAT_INITIALIZED_CLASS_COUNT,
    IL2CPP_STAT_METHOD_COUNT,
    IL2CPP_STAT_CLASS_STATIC_DATA_SIZE,
    IL2CPP_STAT_GENERIC_INSTANCE_COUNT,
    IL2CPP_STAT_GENERIC_CLASS_COUNT,
    IL2CPP_STAT_INFLATED_METHOD_COUNT,
    IL2CPP_STAT_INFLATED_TYPE_COUNT,
} Il2CppStat;
typedef enum
{
    IL2CPP_UNHANDLED_POLICY_LEGACY,
    IL2CPP_UNHANDLED_POLICY_CURRENT
} Il2CppRuntimeUnhandledExceptionPolicy;
typedef struct Il2CppStackFrameInfo
{
    const MethodInfo *method;
    uintptr_t raw_ip;
    int sourceCodeLineNumber;
    int ilOffset;
    const char* filePath;
} Il2CppStackFrameInfo;
typedef void(*Il2CppMethodPointer)();
typedef struct Il2CppMethodDebugInfo
{
    Il2CppMethodPointer methodPointer;
    int32_t code_size;
    const char *file;
} Il2CppMethodDebugInfo;
typedef struct
{
    void* (*malloc_func)(size_t size);
    void* (*aligned_malloc_func)(size_t size, size_t alignment);
    void (*free_func)(void *ptr);
    void (*aligned_free_func)(void *ptr);
    void* (*calloc_func)(size_t nmemb, size_t size);
    void* (*realloc_func)(void *ptr, size_t size);
    void* (*aligned_realloc_func)(void *ptr, size_t size, size_t alignment);
} Il2CppMemoryCallbacks;
typedef struct
{
    const char *name;
    void(*connect)(const char *address);
    int(*wait_for_attach)(void);
    void(*close1)(void);
    void(*close2)(void);
    int(*send)(void *buf, int len);
    int(*recv)(void *buf, int len);
} Il2CppDebuggerTransport;
typedef uint16_t Il2CppChar;
typedef char Il2CppNativeChar;
typedef void (*il2cpp_register_object_callback)(Il2CppObject** arr, int size, void* userdata);
typedef void* (*il2cpp_liveness_reallocate_callback)(void* ptr, size_t size, void* userdata);
typedef void (*Il2CppFrameWalkFunc) (const Il2CppStackFrameInfo *info, void *user_data);
typedef void (*Il2CppProfileFunc) (Il2CppProfiler* prof);
typedef void (*Il2CppProfileMethodFunc) (Il2CppProfiler* prof, const MethodInfo *method);
typedef void (*Il2CppProfileAllocFunc) (Il2CppProfiler* prof, Il2CppObject *obj, Il2CppClass *klass);
typedef void (*Il2CppProfileGCFunc) (Il2CppProfiler* prof, Il2CppGCEvent event, int generation);
typedef void (*Il2CppProfileGCResizeFunc) (Il2CppProfiler* prof, int64_t new_size);
typedef void (*Il2CppProfileFileIOFunc) (Il2CppProfiler* prof, Il2CppProfileFileIOKind kind, int count);
typedef void (*Il2CppProfileThreadFunc) (Il2CppProfiler *prof, unsigned long tid);
typedef const Il2CppNativeChar* (*Il2CppSetFindPlugInCallback)(const Il2CppNativeChar*);
typedef void (*Il2CppLogCallback)(const char*);
typedef size_t(*Il2CppBacktraceFunc) (Il2CppMethodPointer* buffer, size_t maxSize);
typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef uintptr_t il2cpp_array_size_t;
typedef void ( *SynchronizationContextCallback)(intptr_t arg);
typedef void ( *CultureInfoChangedCallback)(const Il2CppChar* arg);
typedef uint16_t Il2CppMethodSlot;
static const uint16_t kInvalidIl2CppMethodSlot = 65535;
static const int ipv6AddressSize = 16;
typedef int32_t il2cpp_hresult_t;
typedef enum
{
    IL2CPP_TOKEN_MODULE = 0x00000000,
    IL2CPP_TOKEN_TYPE_REF = 0x01000000,
    IL2CPP_TOKEN_TYPE_DEF = 0x02000000,
    IL2CPP_TOKEN_FIELD_DEF = 0x04000000,
    IL2CPP_TOKEN_METHOD_DEF = 0x06000000,
    IL2CPP_TOKEN_PARAM_DEF = 0x08000000,
    IL2CPP_TOKEN_INTERFACE_IMPL = 0x09000000,
    IL2CPP_TOKEN_MEMBER_REF = 0x0a000000,
    IL2CPP_TOKEN_CUSTOM_ATTRIBUTE = 0x0c000000,
    IL2CPP_TOKEN_PERMISSION = 0x0e000000,
    IL2CPP_TOKEN_SIGNATURE = 0x11000000,
    IL2CPP_TOKEN_EVENT = 0x14000000,
    IL2CPP_TOKEN_PROPERTY = 0x17000000,
    IL2CPP_TOKEN_MODULE_REF = 0x1a000000,
    IL2CPP_TOKEN_TYPE_SPEC = 0x1b000000,
    IL2CPP_TOKEN_ASSEMBLY = 0x20000000,
    IL2CPP_TOKEN_ASSEMBLY_REF = 0x23000000,
    IL2CPP_TOKEN_FILE = 0x26000000,
    IL2CPP_TOKEN_EXPORTED_TYPE = 0x27000000,
    IL2CPP_TOKEN_MANIFEST_RESOURCE = 0x28000000,
    IL2CPP_TOKEN_GENERIC_PARAM = 0x2a000000,
    IL2CPP_TOKEN_METHOD_SPEC = 0x2b000000,
} Il2CppTokenType;
typedef int32_t TypeIndex;
typedef int32_t TypeDefinitionIndex;
typedef int32_t FieldIndex;
typedef int32_t DefaultValueIndex;
typedef int32_t DefaultValueDataIndex;
typedef int32_t CustomAttributeIndex;
typedef int32_t ParameterIndex;
typedef int32_t MethodIndex;
typedef int32_t GenericMethodIndex;
typedef int32_t PropertyIndex;
typedef int32_t EventIndex;
typedef int32_t GenericContainerIndex;
typedef int32_t GenericParameterIndex;
typedef int16_t GenericParameterConstraintIndex;
typedef int32_t NestedTypeIndex;
typedef int32_t InterfacesIndex;
typedef int32_t VTableIndex;
typedef int32_t RGCTXIndex;
typedef int32_t StringIndex;
typedef int32_t StringLiteralIndex;
typedef int32_t GenericInstIndex;
typedef int32_t ImageIndex;
typedef int32_t AssemblyIndex;
typedef int32_t InteropDataIndex;
typedef int32_t TypeFieldIndex;
typedef int32_t TypeMethodIndex;
typedef int32_t MethodParameterIndex;
typedef int32_t TypePropertyIndex;
typedef int32_t TypeEventIndex;
typedef int32_t TypeInterfaceIndex;
typedef int32_t TypeNestedTypeIndex;
typedef int32_t TypeInterfaceOffsetIndex;
typedef int32_t GenericContainerParameterIndex;
typedef int32_t AssemblyTypeIndex;
typedef int32_t AssemblyExportedTypeIndex;
static const TypeIndex kTypeIndexInvalid = -1;
static const TypeDefinitionIndex kTypeDefinitionIndexInvalid = -1;
static const DefaultValueDataIndex kDefaultValueIndexNull = -1;
static const CustomAttributeIndex kCustomAttributeIndexInvalid = -1;
static const EventIndex kEventIndexInvalid = -1;
static const FieldIndex kFieldIndexInvalid = -1;
static const MethodIndex kMethodIndexInvalid = -1;
static const PropertyIndex kPropertyIndexInvalid = -1;
static const GenericContainerIndex kGenericContainerIndexInvalid = -1;
static const GenericParameterIndex kGenericParameterIndexInvalid = -1;
static const RGCTXIndex kRGCTXIndexInvalid = -1;
static const StringLiteralIndex kStringLiteralIndexInvalid = -1;
static const InteropDataIndex kInteropDataIndexInvalid = -1;
static const int kPublicKeyByteLength = 8;
typedef struct Il2CppMethodSpec
{
    MethodIndex methodDefinitionIndex;
    GenericInstIndex classIndexIndex;
    GenericInstIndex methodIndexIndex;
} Il2CppMethodSpec;
typedef enum Il2CppRGCTXDataType
{
    IL2CPP_RGCTX_DATA_INVALID,
    IL2CPP_RGCTX_DATA_TYPE,
    IL2CPP_RGCTX_DATA_CLASS,
    IL2CPP_RGCTX_DATA_METHOD,
    IL2CPP_RGCTX_DATA_ARRAY,
    IL2CPP_RGCTX_DATA_CONSTRAINED,
} Il2CppRGCTXDataType;
typedef union Il2CppRGCTXDefinitionData
{
    int32_t rgctxDataDummy;
    MethodIndex __methodIndex;
    TypeIndex __typeIndex;
} Il2CppRGCTXDefinitionData;
typedef struct Il2CppRGCTXConstrainedData
{
    TypeIndex __typeIndex;
    uint32_t __encodedMethodIndex;
} Il2CppRGCTXConstrainedData;
typedef struct Il2CppRGCTXDefinition
{
    Il2CppRGCTXDataType type;
    const void* data;
} Il2CppRGCTXDefinition;
typedef struct
{
    MethodIndex methodIndex;
    MethodIndex invokerIndex;
    MethodIndex adjustorThunkIndex;
} Il2CppGenericMethodIndices;
typedef struct Il2CppGenericMethodFunctionsDefinitions
{
    GenericMethodIndex genericMethodIndex;
    Il2CppGenericMethodIndices indices;
} Il2CppGenericMethodFunctionsDefinitions;
static inline uint32_t GetTokenType(uint32_t token)
{
    return token & 0xFF000000;
}
static inline uint32_t GetTokenRowId(uint32_t token)
{
    return token & 0x00FFFFFF;
}
typedef const struct ___Il2CppMetadataImageHandle* Il2CppMetadataImageHandle;
typedef const struct ___Il2CppMetadataCustomAttributeHandle* Il2CppMetadataCustomAttributeHandle;
typedef const struct ___Il2CppMetadataTypeHandle* Il2CppMetadataTypeHandle;
typedef const struct ___Il2CppMetadataMethodHandle* Il2CppMetadataMethodDefinitionHandle;
typedef const struct ___Il2CppMetadataGenericContainerHandle* Il2CppMetadataGenericContainerHandle;
typedef const struct ___Il2CppMetadataGenericParameterHandle* Il2CppMetadataGenericParameterHandle;
typedef uint32_t EncodedMethodIndex;
typedef enum Il2CppMetadataUsage
{
    kIl2CppMetadataUsageInvalid,
    kIl2CppMetadataUsageTypeInfo,
    kIl2CppMetadataUsageIl2CppType,
    kIl2CppMetadataUsageMethodDef,
    kIl2CppMetadataUsageFieldInfo,
    kIl2CppMetadataUsageStringLiteral,
    kIl2CppMetadataUsageMethodRef,
} Il2CppMetadataUsage;
typedef enum Il2CppInvalidMetadataUsageToken
{
    kIl2CppInvalidMetadataUsageNoData = 0,
    kIl2CppInvalidMetadataUsageAmbiguousMethod = 1,
} Il2CppInvalidMetadataUsageToken;
typedef struct Il2CppInterfaceOffsetPair
{
    TypeIndex interfaceTypeIndex;
    int32_t offset;
} Il2CppInterfaceOffsetPair;
typedef struct Il2CppTypeDefinition
{
    StringIndex nameIndex;
    StringIndex namespaceIndex;
    TypeIndex byvalTypeIndex;
    TypeIndex declaringTypeIndex;
    TypeIndex parentIndex;
    TypeIndex elementTypeIndex;
    GenericContainerIndex genericContainerIndex;
    uint32_t flags;
    FieldIndex fieldStart;
    MethodIndex methodStart;
    EventIndex eventStart;
    PropertyIndex propertyStart;
    NestedTypeIndex nestedTypesStart;
    InterfacesIndex interfacesStart;
    VTableIndex vtableStart;
    InterfacesIndex interfaceOffsetsStart;
    uint16_t method_count;
    uint16_t property_count;
    uint16_t field_count;
    uint16_t event_count;
    uint16_t nested_type_count;
    uint16_t vtable_count;
    uint16_t interfaces_count;
    uint16_t interface_offsets_count;
    uint32_t bitfield;
    uint32_t token;
} Il2CppTypeDefinition;
typedef struct Il2CppFieldDefinition
{
    StringIndex nameIndex;
    TypeIndex typeIndex;
    uint32_t token;
} Il2CppFieldDefinition;
typedef struct Il2CppFieldDefaultValue
{
    FieldIndex fieldIndex;
    TypeIndex typeIndex;
    DefaultValueDataIndex dataIndex;
} Il2CppFieldDefaultValue;
typedef struct Il2CppFieldMarshaledSize
{
    FieldIndex fieldIndex;
    TypeIndex typeIndex;
    int32_t size;
} Il2CppFieldMarshaledSize;
typedef struct Il2CppFieldRef
{
    TypeIndex typeIndex;
    FieldIndex fieldIndex;
} Il2CppFieldRef;
typedef struct Il2CppParameterDefinition
{
    StringIndex nameIndex;
    uint32_t token;
    TypeIndex typeIndex;
} Il2CppParameterDefinition;
typedef struct Il2CppParameterDefaultValue
{
    ParameterIndex parameterIndex;
    TypeIndex typeIndex;
    DefaultValueDataIndex dataIndex;
} Il2CppParameterDefaultValue;
typedef struct Il2CppMethodDefinition
{
    StringIndex nameIndex;
    TypeDefinitionIndex declaringType;
    TypeIndex returnType;
    ParameterIndex parameterStart;
    GenericContainerIndex genericContainerIndex;
    uint32_t token;
    uint16_t flags;
    uint16_t iflags;
    uint16_t slot;
    uint16_t parameterCount;
} Il2CppMethodDefinition;
typedef struct Il2CppEventDefinition
{
    StringIndex nameIndex;
    TypeIndex typeIndex;
    MethodIndex add;
    MethodIndex remove;
    MethodIndex raise;
    uint32_t token;
} Il2CppEventDefinition;
typedef struct Il2CppPropertyDefinition
{
    StringIndex nameIndex;
    MethodIndex get;
    MethodIndex set;
    uint32_t attrs;
    uint32_t token;
} Il2CppPropertyDefinition;
typedef struct Il2CppStringLiteral
{
    uint32_t length;
    StringLiteralIndex dataIndex;
} Il2CppStringLiteral;
typedef struct Il2CppAssemblyNameDefinition
{
    StringIndex nameIndex;
    StringIndex cultureIndex;
    StringIndex publicKeyIndex;
    uint32_t hash_alg;
    int32_t hash_len;
    uint32_t flags;
    int32_t major;
    int32_t minor;
    int32_t build;
    int32_t revision;
    uint8_t public_key_token[8];
} Il2CppAssemblyNameDefinition;
typedef struct Il2CppImageDefinition
{
    StringIndex nameIndex;
    AssemblyIndex assemblyIndex;
    TypeDefinitionIndex typeStart;
    uint32_t typeCount;
    TypeDefinitionIndex exportedTypeStart;
    uint32_t exportedTypeCount;
    MethodIndex entryPointIndex;
    uint32_t token;
    CustomAttributeIndex customAttributeStart;
    uint32_t customAttributeCount;
} Il2CppImageDefinition;
typedef struct Il2CppAssemblyDefinition
{
    ImageIndex imageIndex;
    uint32_t token;
    int32_t referencedAssemblyStart;
    int32_t referencedAssemblyCount;
    Il2CppAssemblyNameDefinition aname;
} Il2CppAssemblyDefinition;
typedef struct Il2CppCustomAttributeDataRange
{
    uint32_t token;
    uint32_t startOffset;
} Il2CppCustomAttributeDataRange;
typedef struct Il2CppMetadataRange
{
    int32_t start;
    int32_t length;
} Il2CppMetadataRange;
typedef struct Il2CppGenericContainer
{
    int32_t ownerIndex;
    int32_t type_argc;
    int32_t is_method;
    GenericParameterIndex genericParameterStart;
} Il2CppGenericContainer;
typedef struct Il2CppGenericParameter
{
    GenericContainerIndex ownerIndex;
    StringIndex nameIndex;
    GenericParameterConstraintIndex constraintsStart;
    int16_t constraintsCount;
    uint16_t num;
    uint16_t flags;
} Il2CppGenericParameter;
typedef struct Il2CppWindowsRuntimeTypeNamePair
{
    StringIndex nameIndex;
    TypeIndex typeIndex;
} Il2CppWindowsRuntimeTypeNamePair;
#pragma pack(push, p1,4)
typedef struct Il2CppGlobalMetadataHeader
{
    int32_t sanity;
    int32_t version;
    int32_t stringLiteralOffset;
    int32_t stringLiteralCount;
    int32_t stringLiteralDataOffset;
    int32_t stringLiteralDataCount;
    int32_t stringOffset;
    int32_t stringCount;
    int32_t eventsOffset;
    int32_t eventsCount;
    int32_t propertiesOffset;
    int32_t propertiesCount;
    int32_t methodsOffset;
    int32_t methodsCount;
    int32_t parameterDefaultValuesOffset;
    int32_t parameterDefaultValuesCount;
    int32_t fieldDefaultValuesOffset;
    int32_t fieldDefaultValuesCount;
    int32_t fieldAndParameterDefaultValueDataOffset;
    int32_t fieldAndParameterDefaultValueDataCount;
    int32_t fieldMarshaledSizesOffset;
    int32_t fieldMarshaledSizesCount;
    int32_t parametersOffset;
    int32_t parametersCount;
    int32_t fieldsOffset;
    int32_t fieldsCount;
    int32_t genericParametersOffset;
    int32_t genericParametersCount;
    int32_t genericParameterConstraintsOffset;
    int32_t genericParameterConstraintsCount;
    int32_t genericContainersOffset;
    int32_t genericContainersCount;
    int32_t nestedTypesOffset;
    int32_t nestedTypesCount;
    int32_t interfacesOffset;
    int32_t interfacesCount;
    int32_t vtableMethodsOffset;
    int32_t vtableMethodsCount;
    int32_t interfaceOffsetsOffset;
    int32_t interfaceOffsetsCount;
    int32_t typeDefinitionsOffset;
    int32_t typeDefinitionsCount;
    int32_t imagesOffset;
    int32_t imagesCount;
    int32_t assembliesOffset;
    int32_t assembliesCount;
    int32_t fieldRefsOffset;
    int32_t fieldRefsCount;
    int32_t referencedAssembliesOffset;
    int32_t referencedAssembliesCount;
    int32_t attributeDataOffset;
    int32_t attributeDataCount;
    int32_t attributeDataRangeOffset;
    int32_t attributeDataRangeCount;
    int32_t unresolvedVirtualCallParameterTypesOffset;
    int32_t unresolvedVirtualCallParameterTypesCount;
    int32_t unresolvedVirtualCallParameterRangesOffset;
    int32_t unresolvedVirtualCallParameterRangesCount;
    int32_t windowsRuntimeTypeNamesOffset;
    int32_t windowsRuntimeTypeNamesSize;
    int32_t windowsRuntimeStringsOffset;
    int32_t windowsRuntimeStringsSize;
    int32_t exportedTypeDefinitionsOffset;
    int32_t exportedTypeDefinitionsCount;
} Il2CppGlobalMetadataHeader;
#pragma pack(pop, p1)
typedef struct Il2CppMetadataField
{
    uint32_t offset;
    uint32_t typeIndex;
    const char* name;
    uint8_t isStatic;
} Il2CppMetadataField;
typedef enum Il2CppMetadataTypeFlags
{
    kNone = 0,
    kValueType = 1 << 0,
    kArray = 1 << 1,
    kArrayRankMask = 0xFFFF0000
} Il2CppMetadataTypeFlags;
typedef struct Il2CppMetadataType
{
    Il2CppMetadataTypeFlags flags;
    Il2CppMetadataField* fields;
    uint32_t fieldCount;
    uint32_t staticsSize;
    uint8_t* statics;
    uint32_t baseOrElementTypeIndex;
    char* name;
    const char* assemblyName;
    uint64_t typeInfoAddress;
    uint32_t size;
} Il2CppMetadataType;
typedef struct Il2CppMetadataSnapshot
{
    uint32_t typeCount;
    Il2CppMetadataType* types;
} Il2CppMetadataSnapshot;
typedef struct Il2CppManagedMemorySection
{
    uint64_t sectionStartAddress;
    uint32_t sectionSize;
    uint8_t* sectionBytes;
} Il2CppManagedMemorySection;
typedef struct Il2CppManagedHeap
{
    uint32_t sectionCount;
    Il2CppManagedMemorySection* sections;
} Il2CppManagedHeap;
typedef struct Il2CppStacks
{
    uint32_t stackCount;
    Il2CppManagedMemorySection* stacks;
} Il2CppStacks;
typedef struct NativeObject
{
    uint32_t gcHandleIndex;
    uint32_t size;
    uint32_t instanceId;
    uint32_t classId;
    uint32_t referencedNativeObjectIndicesCount;
    uint32_t* referencedNativeObjectIndices;
} NativeObject;
typedef struct Il2CppGCHandles
{
    uint32_t trackedObjectCount;
    uint64_t* pointersToObjects;
} Il2CppGCHandles;
typedef struct Il2CppRuntimeInformation
{
    uint32_t pointerSize;
    uint32_t objectHeaderSize;
    uint32_t arrayHeaderSize;
    uint32_t arrayBoundsOffsetInHeader;
    uint32_t arraySizeOffsetInHeader;
    uint32_t allocationGranularity;
} Il2CppRuntimeInformation;
typedef struct Il2CppManagedMemorySnapshot
{
    Il2CppManagedHeap heap;
    Il2CppStacks stacks;
    Il2CppMetadataSnapshot metadata;
    Il2CppGCHandles gcHandles;
    Il2CppRuntimeInformation runtimeInformation;
    void* additionalUserInformation;
} Il2CppManagedMemorySnapshot;
typedef enum Il2CppTypeEnum
{
    IL2CPP_TYPE_END = 0x00,
    IL2CPP_TYPE_VOID = 0x01,
    IL2CPP_TYPE_BOOLEAN = 0x02,
    IL2CPP_TYPE_CHAR = 0x03,
    IL2CPP_TYPE_I1 = 0x04,
    IL2CPP_TYPE_U1 = 0x05,
    IL2CPP_TYPE_I2 = 0x06,
    IL2CPP_TYPE_U2 = 0x07,
    IL2CPP_TYPE_I4 = 0x08,
    IL2CPP_TYPE_U4 = 0x09,
    IL2CPP_TYPE_I8 = 0x0a,
    IL2CPP_TYPE_U8 = 0x0b,
    IL2CPP_TYPE_R4 = 0x0c,
    IL2CPP_TYPE_R8 = 0x0d,
    IL2CPP_TYPE_STRING = 0x0e,
    IL2CPP_TYPE_PTR = 0x0f,
    IL2CPP_TYPE_BYREF = 0x10,
    IL2CPP_TYPE_VALUETYPE = 0x11,
    IL2CPP_TYPE_CLASS = 0x12,
    IL2CPP_TYPE_VAR = 0x13,
    IL2CPP_TYPE_ARRAY = 0x14,
    IL2CPP_TYPE_GENERICINST = 0x15,
    IL2CPP_TYPE_TYPEDBYREF = 0x16,
    IL2CPP_TYPE_I = 0x18,
    IL2CPP_TYPE_U = 0x19,
    IL2CPP_TYPE_FNPTR = 0x1b,
    IL2CPP_TYPE_OBJECT = 0x1c,
    IL2CPP_TYPE_SZARRAY = 0x1d,
    IL2CPP_TYPE_MVAR = 0x1e,
    IL2CPP_TYPE_CMOD_REQD = 0x1f,
    IL2CPP_TYPE_CMOD_OPT = 0x20,
    IL2CPP_TYPE_INTERNAL = 0x21,
    IL2CPP_TYPE_MODIFIER = 0x40,
    IL2CPP_TYPE_SENTINEL = 0x41,
    IL2CPP_TYPE_PINNED = 0x45,
    IL2CPP_TYPE_ENUM = 0x55,
    IL2CPP_TYPE_IL2CPP_TYPE_INDEX = 0xff
} Il2CppTypeEnum;
typedef struct Il2CppClass Il2CppClass;
typedef struct MethodInfo MethodInfo;
typedef struct Il2CppType Il2CppType;
typedef struct Il2CppArrayType
{
    const Il2CppType* etype;
    uint8_t rank;
    uint8_t numsizes;
    uint8_t numlobounds;
    int *sizes;
    int *lobounds;
} Il2CppArrayType;
typedef struct Il2CppGenericInst
{
    uint32_t type_argc;
    const Il2CppType **type_argv;
} Il2CppGenericInst;
typedef struct Il2CppGenericContext
{
    const Il2CppGenericInst *class_inst;
    const Il2CppGenericInst *method_inst;
} Il2CppGenericContext;
typedef struct Il2CppGenericClass
{
    const Il2CppType* type;
    Il2CppGenericContext context;
    Il2CppClass *cached_class;
} Il2CppGenericClass;
typedef struct Il2CppGenericMethod
{
    const MethodInfo* methodDefinition;
    Il2CppGenericContext context;
} Il2CppGenericMethod;
typedef struct Il2CppType
{
    union
    {
        void* dummy;
        TypeDefinitionIndex __klassIndex;
        Il2CppMetadataTypeHandle typeHandle;
        const Il2CppType *type;
        Il2CppArrayType *array;
        GenericParameterIndex __genericParameterIndex;
        Il2CppMetadataGenericParameterHandle genericParameterHandle;
        Il2CppGenericClass *generic_class;
    } data;
    unsigned int attrs : 16;
    Il2CppTypeEnum type : 8;
    unsigned int num_mods : 5;
    unsigned int byref : 1;
    unsigned int pinned : 1;
    unsigned int valuetype : 1;
} Il2CppType;
typedef struct Il2CppMetadataFieldInfo
{
    const Il2CppType* type;
    const char* name;
    uint32_t token;
} Il2CppMetadataFieldInfo;
typedef struct Il2CppMetadataMethodInfo
{
    Il2CppMetadataMethodDefinitionHandle handle;
    const char* name;
    const Il2CppType* return_type;
    uint32_t token;
    uint16_t flags;
    uint16_t iflags;
    uint16_t slot;
    uint16_t parameterCount;
} Il2CppMetadataMethodInfo;
typedef struct Il2CppMetadataParameterInfo
{
    const char* name;
    uint32_t token;
    const Il2CppType* type;
} Il2CppMetadataParameterInfo;
typedef struct Il2CppMetadataPropertyInfo
{
    const char* name;
    const MethodInfo* get;
    const MethodInfo* set;
    uint32_t attrs;
    uint32_t token;
} Il2CppMetadataPropertyInfo;
typedef struct Il2CppMetadataEventInfo
{
    const char* name;
    const Il2CppType* type;
    const MethodInfo* add;
    const MethodInfo* remove;
    const MethodInfo* raise;
    uint32_t token;
} Il2CppMetadataEventInfo;
typedef struct Il2CppInterfaceOffsetInfo
{
    const Il2CppType* interfaceType;
    int32_t offset;
} Il2CppInterfaceOffsetInfo;
typedef struct Il2CppGenericParameterInfo
{
    Il2CppMetadataGenericContainerHandle containerHandle;
    const char* name;
    uint16_t num;
    uint16_t flags;
} Il2CppGenericParameterInfo;
typedef enum Il2CppCallConvention
{
    IL2CPP_CALL_DEFAULT,
    IL2CPP_CALL_C,
    IL2CPP_CALL_STDCALL,
    IL2CPP_CALL_THISCALL,
    IL2CPP_CALL_FASTCALL,
    IL2CPP_CALL_VARARG
} Il2CppCallConvention;
typedef enum Il2CppCharSet
{
    CHARSET_ANSI,
    CHARSET_UNICODE,
    CHARSET_NOT_SPECIFIED
} Il2CppCharSet;
typedef struct Il2CppHString__
{
    int unused;
} Il2CppHString__;
typedef Il2CppHString__* Il2CppHString;
typedef struct Il2CppHStringHeader
{
    union
    {
        void* Reserved1;
        char Reserved2[24];
    } Reserved;
} Il2CppHStringHeader;
typedef struct Il2CppGuid
{
    uint32_t data1;
    uint16_t data2;
    uint16_t data3;
    uint8_t data4[8];
} Il2CppGuid;
typedef struct Il2CppSafeArrayBound
{
    uint32_t element_count;
    int32_t lower_bound;
} Il2CppSafeArrayBound;
typedef struct Il2CppSafeArray
{
    uint16_t dimension_count;
    uint16_t features;
    uint32_t element_size;
    uint32_t lock_count;
    void* data;
    Il2CppSafeArrayBound bounds[1];
} Il2CppSafeArray;
typedef struct Il2CppWin32Decimal
{
    uint16_t reserved;
    union
    {
        struct
        {
            uint8_t scale;
            uint8_t sign;
        } s;
        uint16_t signscale;
    } u;
    uint32_t hi32;
    union
    {
        struct
        {
            uint32_t lo32;
            uint32_t mid32;
        } s2;
        uint64_t lo64;
    } u2;
} Il2CppWin32Decimal;
typedef int16_t IL2CPP_VARIANT_BOOL;
typedef enum Il2CppVarType
{
    IL2CPP_VT_EMPTY = 0,
    IL2CPP_VT_NULL = 1,
    IL2CPP_VT_I2 = 2,
    IL2CPP_VT_I4 = 3,
    IL2CPP_VT_R4 = 4,
    IL2CPP_VT_R8 = 5,
    IL2CPP_VT_CY = 6,
    IL2CPP_VT_DATE = 7,
    IL2CPP_VT_BSTR = 8,
    IL2CPP_VT_DISPATCH = 9,
    IL2CPP_VT_ERROR = 10,
    IL2CPP_VT_BOOL = 11,
    IL2CPP_VT_VARIANT = 12,
    IL2CPP_VT_UNKNOWN = 13,
    IL2CPP_VT_DECIMAL = 14,
    IL2CPP_VT_I1 = 16,
    IL2CPP_VT_UI1 = 17,
    IL2CPP_VT_UI2 = 18,
    IL2CPP_VT_UI4 = 19,
    IL2CPP_VT_I8 = 20,
    IL2CPP_VT_UI8 = 21,
    IL2CPP_VT_INT = 22,
    IL2CPP_VT_UINT = 23,
    IL2CPP_VT_VOID = 24,
    IL2CPP_VT_HRESULT = 25,
    IL2CPP_VT_PTR = 26,
    IL2CPP_VT_SAFEARRAY = 27,
    IL2CPP_VT_CARRAY = 28,
    IL2CPP_VT_USERDEFINED = 29,
    IL2CPP_VT_LPSTR = 30,
    IL2CPP_VT_LPWSTR = 31,
    IL2CPP_VT_RECORD = 36,
    IL2CPP_VT_INT_PTR = 37,
    IL2CPP_VT_UINT_PTR = 38,
    IL2CPP_VT_FILETIME = 64,
    IL2CPP_VT_BLOB = 65,
    IL2CPP_VT_STREAM = 66,
    IL2CPP_VT_STORAGE = 67,
    IL2CPP_VT_STREAMED_OBJECT = 68,
    IL2CPP_VT_STORED_OBJECT = 69,
    IL2CPP_VT_BLOB_OBJECT = 70,
    IL2CPP_VT_CF = 71,
    IL2CPP_VT_CLSID = 72,
    IL2CPP_VT_VERSIONED_STREAM = 73,
    IL2CPP_VT_BSTR_BLOB = 0xfff,
    IL2CPP_VT_VECTOR = 0x1000,
    IL2CPP_VT_ARRAY = 0x2000,
    IL2CPP_VT_BYREF = 0x4000,
    IL2CPP_VT_RESERVED = 0x8000,
    IL2CPP_VT_ILLEGAL = 0xffff,
    IL2CPP_VT_ILLEGALMASKED = 0xfff,
    IL2CPP_VT_TYPEMASK = 0xfff,
} Il2CppVarType;
typedef struct Il2CppVariant Il2CppVariant;
typedef struct Il2CppIUnknown Il2CppIUnknown;
typedef struct Il2CppVariant
{
    union
    {
        struct __tagVARIANT
        {
            uint16_t type;
            uint16_t reserved1;
            uint16_t reserved2;
            uint16_t reserved3;
            union
            {
                int64_t llVal;
                int32_t lVal;
                uint8_t bVal;
                int16_t iVal;
                float fltVal;
                double dblVal;
                IL2CPP_VARIANT_BOOL boolVal;
                int32_t scode;
                int64_t cyVal;
                double date;
                Il2CppChar* bstrVal;
                Il2CppIUnknown* punkVal;
                void* pdispVal;
                Il2CppSafeArray* parray;
                uint8_t* pbVal;
                int16_t* piVal;
                int32_t* plVal;
                int64_t* pllVal;
                float* pfltVal;
                double* pdblVal;
                IL2CPP_VARIANT_BOOL* pboolVal;
                int32_t* pscode;
                int64_t* pcyVal;
                double* pdate;
                Il2CppChar* pbstrVal;
                Il2CppIUnknown** ppunkVal;
                void** ppdispVal;
                Il2CppSafeArray** pparray;
                struct Il2CppVariant* pvarVal;
                void* byref;
                char cVal;
                uint16_t uiVal;
                uint32_t ulVal;
                uint64_t ullVal;
                int intVal;
                unsigned int uintVal;
                Il2CppWin32Decimal* pdecVal;
                char* pcVal;
                uint16_t* puiVal;
                uint32_t* pulVal;
                uint64_t* pullVal;
                int* pintVal;
                unsigned int* puintVal;
                struct __tagBRECORD
                {
                    void* pvRecord;
                    void* pRecInfo;
                } n4;
            } n3;
        } n2;
        Il2CppWin32Decimal decVal;
    } n1;
} Il2CppVariant;
typedef struct Il2CppFileTime
{
    uint32_t low;
    uint32_t high;
} Il2CppFileTime;
typedef struct Il2CppStatStg
{
    Il2CppChar* name;
    uint32_t type;
    uint64_t size;
    Il2CppFileTime mtime;
    Il2CppFileTime ctime;
    Il2CppFileTime atime;
    uint32_t mode;
    uint32_t locks;
    Il2CppGuid clsid;
    uint32_t state;
    uint32_t reserved;
} Il2CppStatStg;
typedef enum Il2CppWindowsRuntimeTypeKind
{
    kTypeKindPrimitive = 0,
    kTypeKindMetadata,
    kTypeKindCustom
} Il2CppWindowsRuntimeTypeKind;
typedef struct Il2CppWindowsRuntimeTypeName
{
    Il2CppHString typeName;
    enum Il2CppWindowsRuntimeTypeKind typeKind;
} Il2CppWindowsRuntimeTypeName;
typedef void (*PInvokeMarshalToNativeFunc)(void* managedStructure, void* marshaledStructure);
typedef void (*PInvokeMarshalFromNativeFunc)(void* marshaledStructure, void* managedStructure);
typedef void (*PInvokeMarshalCleanupFunc)(void* marshaledStructure);
typedef struct Il2CppIUnknown* (*CreateCCWFunc)(Il2CppObject* obj);
typedef struct Il2CppInteropData
{
    Il2CppMethodPointer delegatePInvokeWrapperFunction;
    PInvokeMarshalToNativeFunc pinvokeMarshalToNativeFunction;
    PInvokeMarshalFromNativeFunc pinvokeMarshalFromNativeFunction;
    PInvokeMarshalCleanupFunc pinvokeMarshalCleanupFunction;
    CreateCCWFunc createCCWFunction;
    const Il2CppGuid* guid;
    const Il2CppType* type;
} Il2CppInteropData;
typedef struct Il2CppCodeGenModule Il2CppCodeGenModule;
typedef struct Il2CppMetadataRegistration Il2CppMetadataRegistration;
typedef struct Il2CppCodeRegistration Il2CppCodeRegistration;
typedef struct Il2CppClass Il2CppClass;
typedef struct Il2CppGuid Il2CppGuid;
typedef struct Il2CppImage Il2CppImage;
typedef struct Il2CppAppDomain Il2CppAppDomain;
typedef struct Il2CppAppDomainSetup Il2CppAppDomainSetup;
typedef struct Il2CppDelegate Il2CppDelegate;
typedef struct Il2CppAppContext Il2CppAppContext;
typedef struct Il2CppNameToTypeHandleHashTable Il2CppNameToTypeHandleHashTable;
typedef struct Il2CppCodeGenModule Il2CppCodeGenModule;
typedef struct Il2CppMetadataRegistration Il2CppMetadataRegistration;
typedef struct Il2CppCodeRegistration Il2CppCodeRegistration;
typedef struct VirtualInvokeData
{
    Il2CppMethodPointer methodPtr;
    const MethodInfo* method;
} VirtualInvokeData;
typedef enum Il2CppTypeNameFormat
{
    IL2CPP_TYPE_NAME_FORMAT_IL,
    IL2CPP_TYPE_NAME_FORMAT_REFLECTION,
    IL2CPP_TYPE_NAME_FORMAT_FULL_NAME,
    IL2CPP_TYPE_NAME_FORMAT_ASSEMBLY_QUALIFIED
} Il2CppTypeNameFormat;
typedef struct Il2CppDefaults
{
    Il2CppImage *corlib;
    Il2CppImage *corlib_gen;
    Il2CppClass *object_class;
    Il2CppClass *byte_class;
    Il2CppClass *void_class;
    Il2CppClass *boolean_class;
    Il2CppClass *sbyte_class;
    Il2CppClass *int16_class;
    Il2CppClass *uint16_class;
    Il2CppClass *int32_class;
    Il2CppClass *uint32_class;
    Il2CppClass *int_class;
    Il2CppClass *uint_class;
    Il2CppClass *int64_class;
    Il2CppClass *uint64_class;
    Il2CppClass *single_class;
    Il2CppClass *double_class;
    Il2CppClass *char_class;
    Il2CppClass *string_class;
    Il2CppClass *enum_class;
    Il2CppClass *array_class;
    Il2CppClass *delegate_class;
    Il2CppClass *multicastdelegate_class;
    Il2CppClass *asyncresult_class;
    Il2CppClass *manualresetevent_class;
    Il2CppClass *typehandle_class;
    Il2CppClass *fieldhandle_class;
    Il2CppClass *methodhandle_class;
    Il2CppClass *systemtype_class;
    Il2CppClass *monotype_class;
    Il2CppClass *exception_class;
    Il2CppClass *threadabortexception_class;
    Il2CppClass *thread_class;
    Il2CppClass *internal_thread_class;
    Il2CppClass *appdomain_class;
    Il2CppClass *appdomain_setup_class;
    Il2CppClass *member_info_class;
    Il2CppClass *field_info_class;
    Il2CppClass *method_info_class;
    Il2CppClass *property_info_class;
    Il2CppClass *event_info_class;
    Il2CppClass *stringbuilder_class;
    Il2CppClass *stack_frame_class;
    Il2CppClass *stack_trace_class;
    Il2CppClass *marshal_class;
    Il2CppClass *typed_reference_class;
    Il2CppClass *marshalbyrefobject_class;
    Il2CppClass *generic_ilist_class;
    Il2CppClass *generic_icollection_class;
    Il2CppClass *generic_ienumerable_class;
    Il2CppClass *generic_ireadonlylist_class;
    Il2CppClass *generic_ireadonlycollection_class;
    Il2CppClass *runtimetype_class;
    Il2CppClass *generic_nullable_class;
    Il2CppClass *il2cpp_com_object_class;
    Il2CppClass *attribute_class;
    Il2CppClass *customattribute_data_class;
    Il2CppClass *customattribute_typed_argument_class;
    Il2CppClass *customattribute_named_argument_class;
    Il2CppClass *version;
    Il2CppClass *culture_info;
    Il2CppClass *async_call_class;
    Il2CppClass *assembly_class;
    Il2CppClass *assembly_name_class;
    Il2CppClass *parameter_info_class;
    Il2CppClass *module_class;
    Il2CppClass *system_exception_class;
    Il2CppClass *argument_exception_class;
    Il2CppClass *wait_handle_class;
    Il2CppClass *safe_handle_class;
    Il2CppClass *sort_key_class;
    Il2CppClass *dbnull_class;
    Il2CppClass *error_wrapper_class;
    Il2CppClass *missing_class;
    Il2CppClass *value_type_class;
    Il2CppClass *threadpool_wait_callback_class;
    MethodInfo *threadpool_perform_wait_callback_method;
    Il2CppClass *mono_method_message_class;
    Il2CppClass* ireference_class;
    Il2CppClass* ireferencearray_class;
    Il2CppClass* ikey_value_pair_class;
    Il2CppClass* key_value_pair_class;
    Il2CppClass* windows_foundation_uri_class;
    Il2CppClass* windows_foundation_iuri_runtime_class_class;
    Il2CppClass* system_uri_class;
    Il2CppClass* system_guid_class;
    Il2CppClass* sbyte_shared_enum;
    Il2CppClass* int16_shared_enum;
    Il2CppClass* int32_shared_enum;
    Il2CppClass* int64_shared_enum;
    Il2CppClass* byte_shared_enum;
    Il2CppClass* uint16_shared_enum;
    Il2CppClass* uint32_shared_enum;
    Il2CppClass* uint64_shared_enum;
    Il2CppClass* il2cpp_fully_shared_type;
    Il2CppClass* il2cpp_fully_shared_struct_type;
} Il2CppDefaults;
extern Il2CppDefaults il2cpp_defaults;
typedef struct Il2CppClass Il2CppClass;
typedef struct MethodInfo MethodInfo;
typedef struct FieldInfo FieldInfo;
typedef struct Il2CppObject Il2CppObject;
typedef struct MemberInfo MemberInfo;
typedef struct CustomAttributesCache
{
    int count;
    Il2CppObject** attributes;
} CustomAttributesCache;
typedef struct FieldInfo
{
    const char* name;
    const Il2CppType* type;
    Il2CppClass *parent;
    int32_t offset;
    uint32_t token;
} FieldInfo;
typedef struct PropertyInfo
{
    Il2CppClass *parent;
    const char *name;
    const MethodInfo *get;
    const MethodInfo *set;
    uint32_t attrs;
    uint32_t token;
} PropertyInfo;
typedef struct EventInfo
{
    const char* name;
    const Il2CppType* eventType;
    Il2CppClass* parent;
    const MethodInfo* add;
    const MethodInfo* remove;
    const MethodInfo* raise;
    uint32_t token;
} EventInfo;
typedef void (*InvokerMethod)(Il2CppMethodPointer, const MethodInfo*, void*, void**, void*);
typedef enum MethodVariableKind
{
    kMethodVariableKind_This,
    kMethodVariableKind_Parameter,
    kMethodVariableKind_LocalVariable
} MethodVariableKind;
typedef enum SequencePointKind
{
    kSequencePointKind_Normal,
    kSequencePointKind_StepOut
} SequencePointKind;
typedef struct Il2CppMethodExecutionContextInfo
{
    TypeIndex typeIndex;
    int32_t nameIndex;
    int32_t scopeIndex;
} Il2CppMethodExecutionContextInfo;
typedef struct Il2CppMethodExecutionContextInfoIndex
{
    int32_t startIndex;
    int32_t count;
} Il2CppMethodExecutionContextInfoIndex;
typedef struct Il2CppMethodScope
{
    int32_t startOffset;
    int32_t endOffset;
} Il2CppMethodScope;
typedef struct Il2CppMethodHeaderInfo
{
    int32_t code_size;
    int32_t startScope;
    int32_t numScopes;
} Il2CppMethodHeaderInfo;
typedef struct Il2CppSequencePointSourceFile
{
    const char *file;
    uint8_t hash[16];
} Il2CppSequencePointSourceFile;
typedef struct Il2CppTypeSourceFilePair
{
    TypeDefinitionIndex __klassIndex;
    int32_t sourceFileIndex;
} Il2CppTypeSourceFilePair;
typedef struct Il2CppSequencePoint
{
    MethodIndex __methodDefinitionIndex;
    int32_t sourceFileIndex;
    int32_t lineStart, lineEnd;
    int32_t columnStart, columnEnd;
    int32_t ilOffset;
    SequencePointKind kind;
    int32_t isActive;
    int32_t id;
} Il2CppSequencePoint;
typedef struct Il2CppCatchPoint
{
    MethodIndex __methodDefinitionIndex;
    TypeIndex catchTypeIndex;
    int32_t ilOffset;
    int32_t tryId;
    int32_t parentTryId;
} Il2CppCatchPoint;
typedef struct Il2CppDebuggerMetadataRegistration
{
    Il2CppMethodExecutionContextInfo* methodExecutionContextInfos;
    Il2CppMethodExecutionContextInfoIndex* methodExecutionContextInfoIndexes;
    Il2CppMethodScope* methodScopes;
    Il2CppMethodHeaderInfo* methodHeaderInfos;
    Il2CppSequencePointSourceFile* sequencePointSourceFiles;
    int32_t numSequencePoints;
    Il2CppSequencePoint* sequencePoints;
    int32_t numCatchPoints;
    Il2CppCatchPoint* catchPoints;
    int32_t numTypeSourceFileEntries;
    Il2CppTypeSourceFilePair* typeSourceFiles;
    const char** methodExecutionContextInfoStrings;
} Il2CppDebuggerMetadataRegistration;
typedef union Il2CppRGCTXData
{
    void* rgctxDataDummy;
    const MethodInfo* method;
    const Il2CppType* type;
    Il2CppClass* klass;
} Il2CppRGCTXData;
typedef struct MethodInfo
{
    Il2CppMethodPointer methodPointer;
    Il2CppMethodPointer virtualMethodPointer;
    InvokerMethod invoker_method;
    const char* name;
    Il2CppClass *klass;
    const Il2CppType *return_type;
    const Il2CppType** parameters;
    union
    {
        const Il2CppRGCTXData* rgctx_data;
        Il2CppMetadataMethodDefinitionHandle methodMetadataHandle;
    };
    union
    {
        const Il2CppGenericMethod* genericMethod;
        Il2CppMetadataGenericContainerHandle genericContainerHandle;
    };
    uint32_t token;
    uint16_t flags;
    uint16_t iflags;
    uint16_t slot;
    uint8_t parameters_count;
    uint8_t is_generic : 1;
    uint8_t is_inflated : 1;
    uint8_t wrapper_type : 1;
    uint8_t has_full_generic_sharing_signature : 1;
    uint8_t indirect_call_via_invokers : 1;
} MethodInfo;
typedef struct Il2CppRuntimeInterfaceOffsetPair
{
    Il2CppClass* interfaceType;
    int32_t offset;
} Il2CppRuntimeInterfaceOffsetPair;
typedef struct Il2CppClass
{
    const Il2CppImage* image;
    void* gc_desc;
    const char* name;
    const char* namespaze;
    Il2CppType byval_arg;
    Il2CppType this_arg;
    Il2CppClass* element_class;
    Il2CppClass* castClass;
    Il2CppClass* declaringType;
    Il2CppClass* parent;
    Il2CppGenericClass *generic_class;
    Il2CppMetadataTypeHandle typeMetadataHandle;
    const Il2CppInteropData* interopData;
    Il2CppClass* klass;
    FieldInfo* fields;
    const EventInfo* events;
    const PropertyInfo* properties;
    const MethodInfo** methods;
    Il2CppClass** nestedTypes;
    Il2CppClass** implementedInterfaces;
    Il2CppRuntimeInterfaceOffsetPair* interfaceOffsets;
    void* static_fields;
    const Il2CppRGCTXData* rgctx_data;
    struct Il2CppClass** typeHierarchy;
    void *unity_user_data;
    uint32_t initializationExceptionGCHandle;
    uint32_t cctor_started;
    uint32_t cctor_finished_or_no_cctor;
    __declspec(align(8)) size_t cctor_thread;
    Il2CppMetadataGenericContainerHandle genericContainerHandle;
    uint32_t instance_size;
    uint32_t actualSize;
    uint32_t element_size;
    int32_t native_size;
    uint32_t static_fields_size;
    uint32_t thread_static_fields_size;
    int32_t thread_static_fields_offset;
    uint32_t flags;
    uint32_t token;
    uint16_t method_count;
    uint16_t property_count;
    uint16_t field_count;
    uint16_t event_count;
    uint16_t nested_type_count;
    uint16_t vtable_count;
    uint16_t interfaces_count;
    uint16_t interface_offsets_count;
    uint8_t typeHierarchyDepth;
    uint8_t genericRecursionDepth;
    uint8_t rank;
    uint8_t minimumAlignment;
    uint8_t naturalAligment;
    uint8_t packingSize;
    uint8_t initialized_and_no_error : 1;
    uint8_t initialized : 1;
    uint8_t enumtype : 1;
    uint8_t nullabletype : 1;
    uint8_t is_generic : 1;
    uint8_t has_references : 1;
    uint8_t init_pending : 1;
    uint8_t size_init_pending : 1;
    uint8_t size_inited : 1;
    uint8_t has_finalize : 1;
    uint8_t has_cctor : 1;
    uint8_t is_blittable : 1;
    uint8_t is_import_or_windows_runtime : 1;
    uint8_t is_vtable_initialized : 1;
    uint8_t is_byref_like : 1;
    VirtualInvokeData vtable[32];
} Il2CppClass;

typedef struct Il2CppClass_0 {
    const Il2CppImage* image;
    void* gc_desc;
    const char* name;
    const char* namespaze;
    Il2CppType byval_arg;
    Il2CppType this_arg;
    Il2CppClass* element_class;
    Il2CppClass* castClass;
    Il2CppClass* declaringType;
    Il2CppClass* parent;
    Il2CppGenericClass * generic_class;
    Il2CppMetadataTypeHandle typeMetadataHandle;
    const Il2CppInteropData* interopData;
    Il2CppClass* klass;
    FieldInfo* fields;
    const EventInfo* events;
    const PropertyInfo* properties;
    const MethodInfo** methods;
    Il2CppClass** nestedTypes;
    Il2CppClass** implementedInterfaces;
} Il2CppClass_0;

typedef struct Il2CppClass_1 {
    struct Il2CppClass** typeHierarchy;
    void * unity_user_data;
    uint32_t initializationExceptionGCHandle;
    uint32_t cctor_started;
    uint32_t cctor_finished_or_no_cctor;
#ifdef IS_32BIT
    uint32_t cctor_thread;
#else
    __declspec(align(8)) size_t cctor_thread;
#endif
    Il2CppMetadataGenericContainerHandle genericContainerHandle;
    uint32_t instance_size;
    uint32_t actualSize;
    uint32_t element_size;
    int32_t native_size;
    uint32_t static_fields_size;
    uint32_t thread_static_fields_size;
    int32_t thread_static_fields_offset;
    uint32_t flags;
    uint32_t token;
    uint16_t method_count;
    uint16_t property_count;
    uint16_t field_count;
    uint16_t event_count;
    uint16_t nested_type_count;
    uint16_t vtable_count;
    uint16_t interfaces_count;
    uint16_t interface_offsets_count;
    uint8_t typeHierarchyDepth;
    uint8_t genericRecursionDepth;
    uint8_t rank;
    uint8_t minimumAlignment;
    uint8_t naturalAligment;
    uint8_t packingSize;
    uint8_t initialized_and_no_error : 1;
    uint8_t initialized : 1;
    uint8_t enumtype : 1;
    uint8_t nullabletype : 1;
    uint8_t is_generic : 1;
    uint8_t has_references : 1;
    uint8_t init_pending : 1;
    uint8_t size_init_pending : 1;
    uint8_t size_inited : 1;
    uint8_t has_finalize : 1;
    uint8_t has_cctor : 1;
    uint8_t is_blittable : 1;
    uint8_t is_import_or_windows_runtime : 1;
    uint8_t is_vtable_initialized : 1;
    uint8_t is_byref_like : 1;
} Il2CppClass_1;

typedef struct __declspec(align(8)) Il2CppClass_Merged {
    struct Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair* interfaceOffsets;
    void* static_fields;
    const Il2CppRGCTXData* rgctx_data;
    struct Il2CppClass_1 _1;
    VirtualInvokeData vtable[32];
} Il2CppClass_Merged;

typedef struct Il2CppTypeDefinitionSizes
{
    uint32_t instance_size;
    int32_t native_size;
    uint32_t static_fields_size;
    uint32_t thread_static_fields_size;
} Il2CppTypeDefinitionSizes;
typedef struct Il2CppDomain
{
    Il2CppAppDomain* domain;
    Il2CppAppDomainSetup* setup;
    Il2CppAppContext* default_context;
    const char* friendly_name;
    uint32_t domain_id;
    volatile int threadpool_jobs;
    void* agent_info;
} Il2CppDomain;
typedef struct Il2CppAssemblyName
{
    const char* name;
    const char* culture;
    const uint8_t* public_key;
    uint32_t hash_alg;
    int32_t hash_len;
    uint32_t flags;
    int32_t major;
    int32_t minor;
    int32_t build;
    int32_t revision;
    uint8_t public_key_token[8];
} Il2CppAssemblyName;
typedef struct Il2CppImage
{
    const char* name;
    const char *nameNoExt;
    Il2CppAssembly* assembly;
    uint32_t typeCount;
    uint32_t exportedTypeCount;
    uint32_t customAttributeCount;
    Il2CppMetadataImageHandle metadataHandle;
    Il2CppNameToTypeHandleHashTable * nameToClassHashTable;
    const Il2CppCodeGenModule* codeGenModule;
    uint32_t token;
    uint8_t dynamic;
} Il2CppImage;
typedef struct Il2CppAssembly
{
    Il2CppImage* image;
    uint32_t token;
    int32_t referencedAssemblyStart;
    int32_t referencedAssemblyCount;
    Il2CppAssemblyName aname;
} Il2CppAssembly;
typedef struct Il2CppCodeGenOptions
{
    uint8_t enablePrimitiveValueTypeGenericSharing;
    int maximumRuntimeGenericDepth;
    int recursiveGenericIterations;
} Il2CppCodeGenOptions;
typedef struct Il2CppRange
{
    int32_t start;
    int32_t length;
} Il2CppRange;
typedef struct Il2CppTokenRangePair
{
    uint32_t token;
    Il2CppRange range;
} Il2CppTokenRangePair;
typedef struct Il2CppTokenIndexMethodTuple
{
    uint32_t token;
    int32_t index;
    void** method;
    uint32_t __genericMethodIndex;
} Il2CppTokenIndexMethodTuple;
typedef struct Il2CppTokenAdjustorThunkPair
{
    uint32_t token;
    Il2CppMethodPointer adjustorThunk;
} Il2CppTokenAdjustorThunkPair;
typedef struct Il2CppWindowsRuntimeFactoryTableEntry
{
    const Il2CppType* type;
    Il2CppMethodPointer createFactoryFunction;
} Il2CppWindowsRuntimeFactoryTableEntry;
typedef struct Il2CppCodeGenModule
{
    const char* moduleName;
    const uint32_t methodPointerCount;
    const Il2CppMethodPointer* methodPointers;
    const uint32_t adjustorThunkCount;
    const Il2CppTokenAdjustorThunkPair* adjustorThunks;
    const int32_t* invokerIndices;
    const uint32_t reversePInvokeWrapperCount;
    const Il2CppTokenIndexMethodTuple* reversePInvokeWrapperIndices;
    const uint32_t rgctxRangesCount;
    const Il2CppTokenRangePair* rgctxRanges;
    const uint32_t rgctxsCount;
    const Il2CppRGCTXDefinition* rgctxs;
    const Il2CppDebuggerMetadataRegistration *debuggerMetadata;
    const Il2CppMethodPointer moduleInitializer;
    TypeDefinitionIndex* staticConstructorTypeIndices;
    const Il2CppMetadataRegistration* metadataRegistration;
    const Il2CppCodeRegistration* codeRegistaration;
} Il2CppCodeGenModule;
typedef struct Il2CppCodeRegistration
{
    uint32_t reversePInvokeWrapperCount;
    const Il2CppMethodPointer* reversePInvokeWrappers;
    uint32_t genericMethodPointersCount;
    const Il2CppMethodPointer* genericMethodPointers;
    const Il2CppMethodPointer* genericAdjustorThunks;
    uint32_t invokerPointersCount;
    const InvokerMethod* invokerPointers;
    uint32_t unresolvedVirtualCallCount;
    const Il2CppMethodPointer* unresolvedVirtualCallPointers;
    uint32_t interopDataCount;
    Il2CppInteropData* interopData;
    uint32_t windowsRuntimeFactoryCount;
    Il2CppWindowsRuntimeFactoryTableEntry* windowsRuntimeFactoryTable;
    uint32_t codeGenModulesCount;
    const Il2CppCodeGenModule** codeGenModules;
} Il2CppCodeRegistration;
typedef struct Il2CppMetadataRegistration
{
    int32_t genericClassesCount;
    Il2CppGenericClass* const * genericClasses;
    int32_t genericInstsCount;
    const Il2CppGenericInst* const * genericInsts;
    int32_t genericMethodTableCount;
    const Il2CppGenericMethodFunctionsDefinitions* genericMethodTable;
    int32_t typesCount;
    const Il2CppType* const * types;
    int32_t methodSpecsCount;
    const Il2CppMethodSpec* methodSpecs;
    FieldIndex fieldOffsetsCount;
    const int32_t** fieldOffsets;
    TypeDefinitionIndex typeDefinitionsSizesCount;
    const Il2CppTypeDefinitionSizes** typeDefinitionsSizes;
    const size_t metadataUsagesCount;
    void** const* metadataUsages;
} Il2CppMetadataRegistration;
typedef struct Il2CppPerfCounters
{
    uint32_t jit_methods;
    uint32_t jit_bytes;
    uint32_t jit_time;
    uint32_t jit_failures;
    uint32_t exceptions_thrown;
    uint32_t exceptions_filters;
    uint32_t exceptions_finallys;
    uint32_t exceptions_depth;
    uint32_t aspnet_requests_queued;
    uint32_t aspnet_requests;
    uint32_t gc_collections0;
    uint32_t gc_collections1;
    uint32_t gc_collections2;
    uint32_t gc_promotions0;
    uint32_t gc_promotions1;
    uint32_t gc_promotion_finalizers;
    uint32_t gc_gen0size;
    uint32_t gc_gen1size;
    uint32_t gc_gen2size;
    uint32_t gc_lossize;
    uint32_t gc_fin_survivors;
    uint32_t gc_num_handles;
    uint32_t gc_allocated;
    uint32_t gc_induced;
    uint32_t gc_time;
    uint32_t gc_total_bytes;
    uint32_t gc_committed_bytes;
    uint32_t gc_reserved_bytes;
    uint32_t gc_num_pinned;
    uint32_t gc_sync_blocks;
    uint32_t remoting_calls;
    uint32_t remoting_channels;
    uint32_t remoting_proxies;
    uint32_t remoting_classes;
    uint32_t remoting_objects;
    uint32_t remoting_contexts;
    uint32_t loader_classes;
    uint32_t loader_total_classes;
    uint32_t loader_appdomains;
    uint32_t loader_total_appdomains;
    uint32_t loader_assemblies;
    uint32_t loader_total_assemblies;
    uint32_t loader_failures;
    uint32_t loader_bytes;
    uint32_t loader_appdomains_uloaded;
    uint32_t thread_contentions;
    uint32_t thread_queue_len;
    uint32_t thread_queue_max;
    uint32_t thread_num_logical;
    uint32_t thread_num_physical;
    uint32_t thread_cur_recognized;
    uint32_t thread_num_recognized;
    uint32_t interop_num_ccw;
    uint32_t interop_num_stubs;
    uint32_t interop_num_marshals;
    uint32_t security_num_checks;
    uint32_t security_num_link_checks;
    uint32_t security_time;
    uint32_t security_depth;
    uint32_t unused;
    uint64_t threadpool_workitems;
    uint64_t threadpool_ioworkitems;
    unsigned int threadpool_threads;
    unsigned int threadpool_iothreads;
} Il2CppPerfCounters;
typedef struct Il2CppClass Il2CppClass;
typedef struct MethodInfo MethodInfo;
typedef struct PropertyInfo PropertyInfo;
typedef struct FieldInfo FieldInfo;
typedef struct EventInfo EventInfo;
typedef struct Il2CppType Il2CppType;
typedef struct Il2CppAssembly Il2CppAssembly;
typedef struct Il2CppException Il2CppException;
typedef struct Il2CppImage Il2CppImage;
typedef struct Il2CppDomain Il2CppDomain;
typedef struct Il2CppString Il2CppString;
typedef struct Il2CppReflectionMethod Il2CppReflectionMethod;
typedef struct Il2CppAsyncCall Il2CppAsyncCall;
typedef struct Il2CppIUnknown Il2CppIUnknown;
typedef struct Il2CppWaitHandle Il2CppWaitHandle;
typedef struct MonitorData MonitorData;
typedef struct Il2CppReflectionAssembly Il2CppReflectionAssembly;
typedef Il2CppClass Il2CppVTable;
typedef struct Il2CppObject
{
    union
    {
        Il2CppClass *klass;
        Il2CppVTable *vtable;
    };
    MonitorData *monitor;
} Il2CppObject;
typedef int32_t il2cpp_array_lower_bound_t;
typedef struct Il2CppArrayBounds
{
    il2cpp_array_size_t length;
    il2cpp_array_lower_bound_t lower_bound;
} Il2CppArrayBounds;
typedef struct Il2CppArray
{
    Il2CppObject obj;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
} Il2CppArray;
typedef struct Il2CppArraySize
{
    Il2CppObject obj;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    __declspec(align(8)) void* vector[32];
} Il2CppArraySize;
typedef struct Il2CppString
{
    Il2CppObject object;
    int32_t length;
    Il2CppChar chars[32];
} Il2CppString;
typedef struct Il2CppReflectionType
{
    Il2CppObject object;
    const Il2CppType *type;
} Il2CppReflectionType;
typedef struct Il2CppReflectionRuntimeType
{
    Il2CppReflectionType type;
    Il2CppObject* type_info;
    Il2CppObject* genericCache;
    Il2CppObject* serializationCtor;
} Il2CppReflectionRuntimeType;
typedef struct Il2CppReflectionMonoType
{
    Il2CppReflectionRuntimeType type;
} Il2CppReflectionMonoType;
typedef struct Il2CppReflectionEvent
{
    Il2CppObject object;
    Il2CppObject *cached_add_event;
} Il2CppReflectionEvent;
typedef struct Il2CppReflectionMonoEvent
{
    Il2CppReflectionEvent event;
    Il2CppReflectionType* reflectedType;
    const EventInfo* eventInfo;
} Il2CppReflectionMonoEvent;
typedef struct Il2CppReflectionMonoEventInfo
{
    Il2CppReflectionType* declaringType;
    Il2CppReflectionType* reflectedType;
    Il2CppString* name;
    Il2CppReflectionMethod* addMethod;
    Il2CppReflectionMethod* removeMethod;
    Il2CppReflectionMethod* raiseMethod;
    uint32_t eventAttributes;
    Il2CppArray* otherMethods;
} Il2CppReflectionMonoEventInfo;
typedef struct Il2CppReflectionField
{
    Il2CppObject object;
    Il2CppClass *klass;
    FieldInfo *field;
    Il2CppString *name;
    Il2CppReflectionType *type;
    uint32_t attrs;
} Il2CppReflectionField;
typedef struct Il2CppReflectionProperty
{
    Il2CppObject object;
    Il2CppClass *klass;
    const PropertyInfo *property;
} Il2CppReflectionProperty;
typedef struct Il2CppReflectionMethod
{
    Il2CppObject object;
    const MethodInfo *method;
    Il2CppString *name;
    Il2CppReflectionType *reftype;
} Il2CppReflectionMethod;
typedef struct Il2CppReflectionGenericMethod
{
    Il2CppReflectionMethod base;
} Il2CppReflectionGenericMethod;
typedef struct Il2CppMethodInfo
{
    Il2CppReflectionType *parent;
    Il2CppReflectionType *ret;
    uint32_t attrs;
    uint32_t implattrs;
    uint32_t callconv;
} Il2CppMethodInfo;
typedef struct Il2CppPropertyInfo
{
    Il2CppReflectionType* parent;
    Il2CppReflectionType* declaringType;
    Il2CppString *name;
    Il2CppReflectionMethod *get;
    Il2CppReflectionMethod *set;
    uint32_t attrs;
} Il2CppPropertyInfo;
typedef struct Il2CppReflectionParameter
{
    Il2CppObject object;
    uint32_t AttrsImpl;
    Il2CppReflectionType *ClassImpl;
    Il2CppObject *DefaultValueImpl;
    Il2CppObject *MemberImpl;
    Il2CppString *NameImpl;
    int32_t PositionImpl;
    Il2CppObject* MarshalAs;
} Il2CppReflectionParameter;
typedef struct Il2CppReflectionModule
{
    Il2CppObject obj;
    const Il2CppImage* image;
    Il2CppReflectionAssembly* assembly;
    Il2CppString* fqname;
    Il2CppString* name;
    Il2CppString* scopename;
    uint8_t is_resource;
    uint32_t token;
} Il2CppReflectionModule;
typedef struct Il2CppReflectionAssemblyName
{
    Il2CppObject obj;
    Il2CppString *name;
    Il2CppString *codebase;
    int32_t major, minor, build, revision;
    Il2CppObject *cultureInfo;
    uint32_t flags;
    uint32_t hashalg;
    Il2CppObject *keypair;
    Il2CppArray *publicKey;
    Il2CppArray *keyToken;
    uint32_t versioncompat;
    Il2CppObject *version;
    uint32_t processor_architecture;
    uint32_t contentType;
} Il2CppReflectionAssemblyName;
typedef struct Il2CppReflectionAssembly
{
    Il2CppObject object;
    const Il2CppAssembly *assembly;
    Il2CppObject *evidence;
    Il2CppObject *resolve_event_holder;
    Il2CppObject *minimum;
    Il2CppObject *optional;
    Il2CppObject *refuse;
    Il2CppObject *granted;
    Il2CppObject *denied;
    uint8_t from_byte_array;
    Il2CppString *name;
} Il2CppReflectionAssembly;
typedef struct Il2CppReflectionMarshal
{
    Il2CppObject object;
    int32_t count;
    int32_t type;
    int32_t eltype;
    Il2CppString* guid;
    Il2CppString* mcookie;
    Il2CppString* marshaltype;
    Il2CppObject* marshaltyperef;
    int32_t param_num;
    uint8_t has_size;
} Il2CppReflectionMarshal;
typedef struct Il2CppReflectionPointer
{
    Il2CppObject object;
    void* data;
    Il2CppReflectionType* type;
} Il2CppReflectionPointer;
typedef struct Il2CppThreadName
{
    Il2CppChar* chars;
    int32_t unused;
    int32_t length;
} Il2CppThreadName;
typedef struct
{
    uint32_t ref;
    void (*destructor)(void* data);
} Il2CppRefCount;
typedef struct
{
    Il2CppRefCount ref;
    void* synch_cs;
} Il2CppLongLivedThreadData;
typedef struct Il2CppInternalThread
{
    Il2CppObject obj;
    int lock_thread_id;
    void* handle;
    void* native_handle;
    Il2CppThreadName name;
    uint32_t state;
    Il2CppObject* abort_exc;
    int abort_state_handle;
    uint64_t tid;
    intptr_t debugger_thread;
    void** static_data;
    void* runtime_thread_info;
    Il2CppObject* current_appcontext;
    Il2CppObject* root_domain_thread;
    Il2CppArray* _serialized_principal;
    int _serialized_principal_version;
    void* appdomain_refs;
    int32_t interruption_requested;
    void* longlived;
    uint8_t threadpool_thread;
    uint8_t thread_interrupt_requested;
    int stack_size;
    uint8_t apartment_state;
    int critical_region_level;
    int managed_id;
    uint32_t small_id;
    void* manage_callback;
    intptr_t flags;
    void* thread_pinning_ref;
    void* abort_protected_block_count;
    int32_t priority;
    void* owned_mutexes;
    void * suspended;
    int32_t self_suspended;
    size_t thread_state;
    void* unused[3];
    void* last;
} Il2CppInternalThread;
typedef struct Il2CppIOSelectorJob
{
    Il2CppObject object;
    int32_t operation;
    Il2CppObject *callback;
    Il2CppObject *state;
} Il2CppIOSelectorJob;
typedef enum
{
    Il2Cpp_CallType_Sync = 0,
    Il2Cpp_CallType_BeginInvoke = 1,
    Il2Cpp_CallType_EndInvoke = 2,
    Il2Cpp_CallType_OneWay = 3
} Il2CppCallType;
typedef struct Il2CppMethodMessage
{
    Il2CppObject obj;
    Il2CppReflectionMethod *method;
    Il2CppArray *args;
    Il2CppArray *names;
    Il2CppArray *arg_types;
    Il2CppObject *ctx;
    Il2CppObject *rval;
    Il2CppObject *exc;
    Il2CppAsyncResult *async_result;
    uint32_t call_type;
} Il2CppMethodMessage;
typedef struct Il2CppAppDomainSetup
{
    Il2CppObject object;
    Il2CppString* application_base;
    Il2CppString* application_name;
    Il2CppString* cache_path;
    Il2CppString* configuration_file;
    Il2CppString* dynamic_base;
    Il2CppString* license_file;
    Il2CppString* private_bin_path;
    Il2CppString* private_bin_path_probe;
    Il2CppString* shadow_copy_directories;
    Il2CppString* shadow_copy_files;
    uint8_t publisher_policy;
    uint8_t path_changed;
    int loader_optimization;
    uint8_t disallow_binding_redirects;
    uint8_t disallow_code_downloads;
    Il2CppObject* activation_arguments;
    Il2CppObject* domain_initializer;
    Il2CppObject* application_trust;
    Il2CppArray* domain_initializer_args;
    uint8_t disallow_appbase_probe;
    Il2CppArray* configuration_bytes;
    Il2CppArray* serialized_non_primitives;
} Il2CppAppDomainSetup;
typedef struct Il2CppThread
{
    Il2CppObject obj;
    Il2CppInternalThread* internal_thread;
    Il2CppObject* start_obj;
    Il2CppException* pending_exception;
    Il2CppObject* principal;
    int32_t principal_version;
    Il2CppDelegate* delegate;
    Il2CppObject* executionContext;
    uint8_t executionContextBelongsToOuterScope;
} Il2CppThread;
typedef struct Il2CppException
{
    Il2CppObject object;
    Il2CppString* className;
    Il2CppString* message;
    Il2CppObject* _data;
    struct Il2CppException* inner_ex;
    Il2CppString* _helpURL;
    Il2CppArray* trace_ips;
    Il2CppString* stack_trace;
    Il2CppString* remote_stack_trace;
    int remote_stack_index;
    Il2CppObject* _dynamicMethods;
    il2cpp_hresult_t hresult;
    Il2CppString* source;
    Il2CppObject* safeSerializationManager;
    Il2CppArray* captured_traces;
    Il2CppArray* native_trace_ips;
    int32_t caught_in_unmanaged;
} Il2CppException;
typedef struct Il2CppSystemException
{
    Il2CppException base;
} Il2CppSystemException;
typedef struct Il2CppArgumentException
{
    Il2CppException base;
    Il2CppString *argName;
} Il2CppArgumentException;
typedef struct Il2CppTypedRef
{
    const Il2CppType *type;
    void* value;
    Il2CppClass *klass;
} Il2CppTypedRef;
typedef struct Il2CppDelegate
{
    Il2CppObject object;
    Il2CppMethodPointer method_ptr;
    Il2CppMethodPointer invoke_impl;
    Il2CppObject *target;
    const MethodInfo *method;
    void* delegate_trampoline;
    intptr_t extraArg;
    uint8_t **method_code;
    void* interp_method;
    void* interp_invoke_impl;
    Il2CppReflectionMethod *method_info;
    Il2CppReflectionMethod *original_method_info;
    Il2CppObject *data;
    uint8_t method_is_virtual;
} Il2CppDelegate;
typedef struct Il2CppMulticastDelegate
{
    Il2CppDelegate delegate;
    Il2CppArray *delegates;
} Il2CppMulticastDelegate;
typedef struct Il2CppMarshalByRefObject
{
    Il2CppObject obj;
    Il2CppObject *identity;
} Il2CppMarshalByRefObject;
typedef void* Il2CppFullySharedGenericAny;
typedef void* Il2CppFullySharedGenericStruct;
typedef struct Il2CppAppDomain
{
    Il2CppMarshalByRefObject mbr;
    Il2CppDomain *data;
} Il2CppAppDomain;
typedef struct Il2CppStackFrame
{
    Il2CppObject obj;
    int32_t il_offset;
    int32_t native_offset;
    uint64_t methodAddress;
    uint32_t methodIndex;
    Il2CppReflectionMethod *method;
    Il2CppString *filename;
    int32_t line;
    int32_t column;
    Il2CppString *internal_method_name;
} Il2CppStackFrame;
typedef struct Il2CppDateTimeFormatInfo
{
    Il2CppObject obj;
    Il2CppObject* CultureData;
    Il2CppString* Name;
    Il2CppString* LangName;
    Il2CppObject* CompareInfo;
    Il2CppObject* CultureInfo;
    Il2CppString* AMDesignator;
    Il2CppString* PMDesignator;
    Il2CppString* DateSeparator;
    Il2CppString* GeneralShortTimePattern;
    Il2CppString* GeneralLongTimePattern;
    Il2CppString* TimeSeparator;
    Il2CppString* MonthDayPattern;
    Il2CppString* DateTimeOffsetPattern;
    Il2CppObject* Calendar;
    uint32_t FirstDayOfWeek;
    uint32_t CalendarWeekRule;
    Il2CppString* FullDateTimePattern;
    Il2CppArray* AbbreviatedDayNames;
    Il2CppArray* ShortDayNames;
    Il2CppArray* DayNames;
    Il2CppArray* AbbreviatedMonthNames;
    Il2CppArray* MonthNames;
    Il2CppArray* GenitiveMonthNames;
    Il2CppArray* GenitiveAbbreviatedMonthNames;
    Il2CppArray* LeapYearMonthNames;
    Il2CppString* LongDatePattern;
    Il2CppString* ShortDatePattern;
    Il2CppString* YearMonthPattern;
    Il2CppString* LongTimePattern;
    Il2CppString* ShortTimePattern;
    Il2CppArray* YearMonthPatterns;
    Il2CppArray* ShortDatePatterns;
    Il2CppArray* LongDatePatterns;
    Il2CppArray* ShortTimePatterns;
    Il2CppArray* LongTimePatterns;
    Il2CppArray* EraNames;
    Il2CppArray* AbbrevEraNames;
    Il2CppArray* AbbrevEnglishEraNames;
    Il2CppArray* OptionalCalendars;
    uint8_t readOnly;
    int32_t FormatFlags;
    int32_t CultureID;
    uint8_t UseUserOverride;
    uint8_t UseCalendarInfo;
    int32_t DataItem;
    uint8_t IsDefaultCalendar;
    Il2CppArray* DateWords;
    Il2CppString* FullTimeSpanPositivePattern;
    Il2CppString* FullTimeSpanNegativePattern;
    Il2CppArray* dtfiTokenHash;
} Il2CppDateTimeFormatInfo;
typedef struct Il2CppNumberFormatInfo
{
    Il2CppObject obj;
    Il2CppArray* numberGroupSizes;
    Il2CppArray* currencyGroupSizes;
    Il2CppArray* percentGroupSizes;
    Il2CppString* positiveSign;
    Il2CppString* negativeSign;
    Il2CppString* numberDecimalSeparator;
    Il2CppString* numberGroupSeparator;
    Il2CppString* currencyGroupSeparator;
    Il2CppString* currencyDecimalSeparator;
    Il2CppString* currencySymbol;
    Il2CppString* ansiCurrencySymbol;
    Il2CppString* naNSymbol;
    Il2CppString* positiveInfinitySymbol;
    Il2CppString* negativeInfinitySymbol;
    Il2CppString* percentDecimalSeparator;
    Il2CppString* percentGroupSeparator;
    Il2CppString* percentSymbol;
    Il2CppString* perMilleSymbol;
    Il2CppArray* nativeDigits;
    int dataItem;
    int numberDecimalDigits;
    int currencyDecimalDigits;
    int currencyPositivePattern;
    int currencyNegativePattern;
    int numberNegativePattern;
    int percentPositivePattern;
    int percentNegativePattern;
    int percentDecimalDigits;
    int digitSubstitution;
    uint8_t readOnly;
    uint8_t useUserOverride;
    uint8_t isInvariant;
    uint8_t validForParseAsNumber;
    uint8_t validForParseAsCurrency;
} Il2CppNumberFormatInfo;
typedef struct NumberFormatEntryManaged
{
    int32_t currency_decimal_digits;
    int32_t currency_decimal_separator;
    int32_t currency_group_separator;
    int32_t currency_group_sizes0;
    int32_t currency_group_sizes1;
    int32_t currency_negative_pattern;
    int32_t currency_positive_pattern;
    int32_t currency_symbol;
    int32_t nan_symbol;
    int32_t negative_infinity_symbol;
    int32_t negative_sign;
    int32_t number_decimal_digits;
    int32_t number_decimal_separator;
    int32_t number_group_separator;
    int32_t number_group_sizes0;
    int32_t number_group_sizes1;
    int32_t number_negative_pattern;
    int32_t per_mille_symbol;
    int32_t percent_negative_pattern;
    int32_t percent_positive_pattern;
    int32_t percent_symbol;
    int32_t positive_infinity_symbol;
    int32_t positive_sign;
} NumberFormatEntryManaged;
typedef struct Il2CppCultureData
{
    Il2CppObject obj;
    Il2CppString *AMDesignator;
    Il2CppString *PMDesignator;
    Il2CppString *TimeSeparator;
    Il2CppArray *LongTimePatterns;
    Il2CppArray *ShortTimePatterns;
    uint32_t FirstDayOfWeek;
    uint32_t CalendarWeekRule;
} Il2CppCultureData;
typedef struct Il2CppCalendarData
{
    Il2CppObject obj;
    Il2CppString *NativeName;
    Il2CppArray *ShortDatePatterns;
    Il2CppArray *YearMonthPatterns;
    Il2CppArray *LongDatePatterns;
    Il2CppString *MonthDayPattern;
    Il2CppArray *EraNames;
    Il2CppArray *AbbreviatedEraNames;
    Il2CppArray *AbbreviatedEnglishEraNames;
    Il2CppArray *DayNames;
    Il2CppArray *AbbreviatedDayNames;
    Il2CppArray *SuperShortDayNames;
    Il2CppArray *MonthNames;
    Il2CppArray *AbbreviatedMonthNames;
    Il2CppArray *GenitiveMonthNames;
    Il2CppArray *GenitiveAbbreviatedMonthNames;
} Il2CppCalendarData;
typedef struct Il2CppCultureInfo
{
    Il2CppObject obj;
    uint8_t is_read_only;
    int32_t lcid;
    int32_t parent_lcid;
    int32_t datetime_index;
    int32_t number_index;
    int32_t default_calendar_type;
    uint8_t use_user_override;
    Il2CppNumberFormatInfo* number_format;
    Il2CppDateTimeFormatInfo* datetime_format;
    Il2CppObject* textinfo;
    Il2CppString* name;
    Il2CppString* englishname;
    Il2CppString* nativename;
    Il2CppString* iso3lang;
    Il2CppString* iso2lang;
    Il2CppString* win3lang;
    Il2CppString* territory;
    Il2CppArray* native_calendar_names;
    Il2CppString* compareinfo;
    const void* text_info_data;
    int dataItem;
    Il2CppObject* calendar;
    Il2CppObject* parent_culture;
    uint8_t constructed;
    Il2CppArray* cached_serialized_form;
    Il2CppObject* cultureData;
    uint8_t isInherited;
} Il2CppCultureInfo;
typedef struct Il2CppRegionInfo
{
    Il2CppObject obj;
    int32_t geo_id;
    Il2CppString* iso2name;
    Il2CppString* iso3name;
    Il2CppString* win3name;
    Il2CppString* english_name;
    Il2CppString* native_name;
    Il2CppString* currency_symbol;
    Il2CppString* iso_currency_symbol;
    Il2CppString* currency_english_name;
    Il2CppString* currency_native_name;
} Il2CppRegionInfo;
typedef struct Il2CppSafeHandle
{
    Il2CppObject base;
    void* handle;
    int state;
    uint8_t owns_handle;
    uint8_t fullyInitialized;
} Il2CppSafeHandle;
typedef struct Il2CppStringBuilder Il2CppStringBuilder;
typedef struct Il2CppStringBuilder
{
    Il2CppObject object;
    Il2CppArray* chunkChars;
    struct Il2CppStringBuilder* chunkPrevious;
    int chunkLength;
    int chunkOffset;
    int maxCapacity;
} Il2CppStringBuilder;
typedef struct Il2CppSocketAddress
{
    Il2CppObject base;
    int m_Size;
    Il2CppArray* data;
    uint8_t m_changed;
    int m_hash;
} Il2CppSocketAddress;
typedef struct Il2CppSortKey
{
    Il2CppObject base;
    Il2CppString *str;
    Il2CppArray *key;
    int32_t options;
    int32_t lcid;
} Il2CppSortKey;
typedef struct Il2CppErrorWrapper
{
    Il2CppObject base;
    int32_t errorCode;
} Il2CppErrorWrapper;
typedef struct Il2CppAsyncResult
{
    Il2CppObject base;
    Il2CppObject *async_state;
    Il2CppWaitHandle *handle;
    Il2CppDelegate *async_delegate;
    void* data;
    Il2CppAsyncCall *object_data;
    uint8_t sync_completed;
    uint8_t completed;
    uint8_t endinvoke_called;
    Il2CppObject *async_callback;
    Il2CppObject *execution_context;
    Il2CppObject *original_context;
} Il2CppAsyncResult;
typedef struct Il2CppAsyncCall
{
    Il2CppObject base;
    Il2CppMethodMessage *msg;
    MethodInfo *cb_method;
    Il2CppDelegate *cb_target;
    Il2CppObject *state;
    Il2CppObject *res;
    Il2CppArray *out_args;
} Il2CppAsyncCall;
typedef struct Il2CppExceptionWrapper Il2CppExceptionWrapper;
typedef struct Il2CppExceptionWrapper
{
    Il2CppException* ex;
} Il2CppExceptionWrapper;
typedef struct Il2CppIOAsyncResult
{
    Il2CppObject base;
    Il2CppDelegate* callback;
    Il2CppObject* state;
    Il2CppWaitHandle* wait_handle;
    uint8_t completed_synchronously;
    uint8_t completed;
} Il2CppIOAsyncResult;
typedef struct Il2CppSocketAsyncResult
{
    Il2CppIOAsyncResult base;
    Il2CppObject* socket;
    int32_t operation;
    Il2CppException* delayedException;
    Il2CppObject* endPoint;
    Il2CppArray* buffer;
    int32_t offset;
    int32_t size;
    int32_t socket_flags;
    Il2CppObject* acceptSocket;
    Il2CppArray* addresses;
    int32_t port;
    Il2CppObject* buffers;
    uint8_t reuseSocket;
    int32_t currentAddress;
    Il2CppObject* acceptedSocket;
    int32_t total;
    int32_t error;
    int32_t endCalled;
} Il2CppSocketAsyncResult;
typedef enum Il2CppResourceLocation
{
    IL2CPP_RESOURCE_LOCATION_EMBEDDED = 1,
    IL2CPP_RESOURCE_LOCATION_ANOTHER_ASSEMBLY = 2,
    IL2CPP_RESOURCE_LOCATION_IN_MANIFEST = 4
} Il2CppResourceLocation;
typedef struct Il2CppManifestResourceInfo
{
    Il2CppObject object;
    Il2CppReflectionAssembly* assembly;
    Il2CppString* filename;
    uint32_t location;
} Il2CppManifestResourceInfo;
typedef struct Il2CppAppContext
{
    Il2CppObject obj;
    int32_t domain_id;
    int32_t context_id;
    void* static_data;
} Il2CppAppContext;
typedef struct Il2CppDecimal
{
    uint16_t reserved;
    union
    {
        struct
        {
            uint8_t scale;
            uint8_t sign;
        } u;
        uint16_t signscale;
    } u;
    uint32_t Hi32;
    union
    {
        struct
        {
            uint32_t Lo32;
            uint32_t Mid32;
        } v;
        uint64_t Lo64;
    } v;
} Il2CppDecimal;
typedef struct Il2CppDouble
{
    uint32_t mantLo : 32;
    uint32_t mantHi : 20;
    uint32_t exp : 11;
    uint32_t sign : 1;
} Il2CppDouble;
typedef union Il2CppDouble_double
{
    Il2CppDouble s;
    double d;
} Il2CppDouble_double;
typedef enum Il2CppDecimalCompareResult
{
    IL2CPP_DECIMAL_CMP_LT = -1,
    IL2CPP_DECIMAL_CMP_EQ,
    IL2CPP_DECIMAL_CMP_GT
} Il2CppDecimalCompareResult;
typedef struct Il2CppSingle
{
    uint32_t mant : 23;
    uint32_t exp : 8;
    uint32_t sign : 1;
} Il2CppSingle;
typedef union Il2CppSingle_float
{
    Il2CppSingle s;
    float f;
} Il2CppSingle_float;
typedef struct Il2CppByReference
{
    intptr_t value;
} Il2CppByReference;

#pragma warning(disable : 4369)
#pragma warning(disable : 4309)
#pragma warning(disable : 4359)
#if !IS_DECOMPILER
namespace app {
#endif

// ******************************************************************************
// * Application types from method calls
// ******************************************************************************

struct Object {
    struct Object__Class *klass;
    MonitorData *monitor;
};
struct String {
    struct String__Class *klass;
    MonitorData *monitor;
    struct String__Fields fields;
};
struct Type {
    struct Type__Class *klass;
    MonitorData *monitor;
    struct Type__Fields fields;
};
struct Object__Array {
    struct Object__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Object *vector[32];
};
struct String__Array {
    struct String__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct String *vector[32];
};
struct Type__Array {
    struct Type__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Type *vector[32];
};
struct Header {
    struct Header__Class *klass;
    MonitorData *monitor;
    struct Header__Fields fields;
};
struct Header__Array {
    struct Header__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Header *vector[32];
};
struct Object__Array__VTable {
};
struct Object__Array__StaticFields {
};
struct Object__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__Array__VTable vtable;
};
struct String__Array__VTable {
};
struct String__Array__StaticFields {
};
struct String__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct String__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct String__Array__VTable vtable;
};
struct Type__Array__VTable {
};
struct Type__Array__StaticFields {
};
struct Type__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Type__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Type__Array__VTable vtable;
};
struct Object___VTable {
};
struct Object___StaticFields {
};
struct Object___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object___VTable vtable;
};
struct Object__1__VTable {
};
struct Object__1__StaticFields {
};
struct Object__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__1__VTable vtable;
};
struct Header__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Header__StaticFields {
};
struct Header__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Header__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Header__VTable vtable;
};
struct Header__Array__VTable {
};
struct Header__Array__StaticFields {
};
struct Header__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Header__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Header__Array__VTable vtable;
};
struct Type___VTable {
};
struct Type___StaticFields {
};
struct Type___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Type___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Type___VTable vtable;
};
struct Type__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData GetCustomAttributes;
    VirtualInvokeData GetCustomAttributes_1;
    VirtualInvokeData IsDefined;
    VirtualInvokeData get_MemberType;
    VirtualInvokeData __unknown;
    VirtualInvokeData get_DeclaringType;
    VirtualInvokeData get_ReflectedType;
    VirtualInvokeData get_Module;
    VirtualInvokeData __unknown_1;
    VirtualInvokeData __unknown_2;
    VirtualInvokeData __unknown_3;
    VirtualInvokeData get_MetadataToken;
    VirtualInvokeData IsEnumDefined;
    VirtualInvokeData GetEnumName;
    VirtualInvokeData GetEnumNames;
    VirtualInvokeData get_IsSerializable;
    VirtualInvokeData get_ContainsGenericParameters;
    VirtualInvokeData IsSubclassOf;
    VirtualInvokeData IsAssignableFrom;
    VirtualInvokeData GetType;
    VirtualInvokeData __unknown_4;
    VirtualInvokeData __unknown_5;
    VirtualInvokeData __unknown_6;
    VirtualInvokeData __unknown_7;
    VirtualInvokeData __unknown_8;
    VirtualInvokeData get_DeclaringMethod;
    VirtualInvokeData __unknown_9;
    VirtualInvokeData get_IsArray;
    VirtualInvokeData __unknown_10;
    VirtualInvokeData get_IsByRef;
    VirtualInvokeData __unknown_11;
    VirtualInvokeData get_IsPointer;
    VirtualInvokeData __unknown_12;
    VirtualInvokeData get_IsConstructedGenericType;
    VirtualInvokeData get_IsGenericParameter;
    VirtualInvokeData get_IsGenericMethodParameter;
    VirtualInvokeData get_IsGenericType;
    VirtualInvokeData get_IsGenericTypeDefinition;
    VirtualInvokeData get_IsSZArray;
    VirtualInvokeData get_IsVariableBoundArray;
    VirtualInvokeData get_HasElementType;
    VirtualInvokeData __unknown_13;
    VirtualInvokeData __unknown_14;
    VirtualInvokeData GetArrayRank;
    VirtualInvokeData GetGenericTypeDefinition;
    VirtualInvokeData get_GenericTypeArguments;
    VirtualInvokeData GetGenericArguments;
    VirtualInvokeData get_GenericParameterPosition;
    VirtualInvokeData get_GenericParameterAttributes;
    VirtualInvokeData GetGenericParameterConstraints;
    VirtualInvokeData get_Attributes;
    VirtualInvokeData __unknown_15;
    VirtualInvokeData get_IsAbstract;
    VirtualInvokeData get_IsSealed;
    VirtualInvokeData get_IsClass;
    VirtualInvokeData get_IsNestedAssembly;
    VirtualInvokeData get_IsNestedPublic;
    VirtualInvokeData get_IsNotPublic;
    VirtualInvokeData get_IsPublic;
    VirtualInvokeData get_IsExplicitLayout;
    VirtualInvokeData get_IsCOMObject;
    VirtualInvokeData __unknown_16;
    VirtualInvokeData get_IsContextful;
    VirtualInvokeData IsContextfulImpl;
    VirtualInvokeData get_IsCollectible;
    VirtualInvokeData get_IsEnum;
    VirtualInvokeData get_IsMarshalByRef;
    VirtualInvokeData IsMarshalByRefImpl;
    VirtualInvokeData get_IsPrimitive;
    VirtualInvokeData __unknown_17;
    VirtualInvokeData get_IsValueType;
    VirtualInvokeData IsValueTypeImpl;
    VirtualInvokeData get_IsSignatureType;
    VirtualInvokeData GetConstructor;
    VirtualInvokeData GetConstructor_1;
    VirtualInvokeData GetConstructor_2;
    VirtualInvokeData __unknown_18;
    VirtualInvokeData __unknown_19;
    VirtualInvokeData GetEvent;
    VirtualInvokeData __unknown_20;
    VirtualInvokeData __unknown_21;
    VirtualInvokeData GetField;
    VirtualInvokeData __unknown_22;
    VirtualInvokeData GetFields;
    VirtualInvokeData __unknown_23;
    VirtualInvokeData GetMember;
    VirtualInvokeData GetMember_1;
    VirtualInvokeData GetMember_2;
    VirtualInvokeData __unknown_24;
    VirtualInvokeData GetMethod;
    VirtualInvokeData GetMethod_1;
    VirtualInvokeData GetMethod_2;
    VirtualInvokeData GetMethod_3;
    VirtualInvokeData GetMethod_4;
    VirtualInvokeData GetMethod_5;
    VirtualInvokeData __unknown_25;
    VirtualInvokeData GetMethods;
    VirtualInvokeData __unknown_26;
    VirtualInvokeData __unknown_27;
    VirtualInvokeData GetProperty;
    VirtualInvokeData GetProperty_1;
    VirtualInvokeData GetProperty_2;
    VirtualInvokeData GetProperty_3;
    VirtualInvokeData GetProperty_4;
    VirtualInvokeData GetProperty_5;
    VirtualInvokeData __unknown_28;
    VirtualInvokeData GetProperties;
    VirtualInvokeData __unknown_29;
    VirtualInvokeData get_TypeHandle;
    VirtualInvokeData GetTypeCodeImpl;
    VirtualInvokeData __unknown_30;
    VirtualInvokeData __unknown_31;
    VirtualInvokeData InvokeMember;
    VirtualInvokeData __unknown_32;
    VirtualInvokeData __unknown_33;
    VirtualInvokeData IsInstanceOfType;
    VirtualInvokeData IsEquivalentTo;
    VirtualInvokeData GetEnumUnderlyingType;
    VirtualInvokeData GetEnumValues;
    VirtualInvokeData MakeArrayType;
    VirtualInvokeData MakeArrayType_1;
    VirtualInvokeData MakeByRefType;
    VirtualInvokeData MakeGenericType;
    VirtualInvokeData MakePointerType;
    VirtualInvokeData Equals_1;
    VirtualInvokeData get_IsSzArray;
    VirtualInvokeData FormatTypeName;
    VirtualInvokeData get_IsInterface;
    VirtualInvokeData InternalGetNameIfAvailable;
};
struct Type__StaticFields {
    struct Binder *s_defaultBinder;
    uint16_t Delimiter;
    struct Type__Array *EmptyTypes;
    struct Object *Missing;
    struct MemberFilter *FilterAttribute;
    struct MemberFilter *FilterName;
    struct MemberFilter *FilterNameIgnoreCase;
};
struct Type__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Type__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Type__VTable vtable;
};
struct String__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData CompareTo;
    VirtualInvokeData System_Collections_IEnumerable_GetEnumerator;
    VirtualInvokeData System_Collections_Generic_IEnumerable_System_Char__GetEnumerator;
    VirtualInvokeData CompareTo_1;
    VirtualInvokeData Equals_1;
    VirtualInvokeData GetTypeCode;
    VirtualInvokeData System_IConvertible_ToBoolean;
    VirtualInvokeData System_IConvertible_ToChar;
    VirtualInvokeData System_IConvertible_ToSByte;
    VirtualInvokeData System_IConvertible_ToByte;
    VirtualInvokeData System_IConvertible_ToInt16;
    VirtualInvokeData System_IConvertible_ToUInt16;
    VirtualInvokeData System_IConvertible_ToInt32;
    VirtualInvokeData System_IConvertible_ToUInt32;
    VirtualInvokeData System_IConvertible_ToInt64;
    VirtualInvokeData System_IConvertible_ToUInt64;
    VirtualInvokeData System_IConvertible_ToSingle;
    VirtualInvokeData System_IConvertible_ToDouble;
    VirtualInvokeData System_IConvertible_ToDecimal;
    VirtualInvokeData System_IConvertible_ToDateTime;
    VirtualInvokeData ToString_1;
    VirtualInvokeData System_IConvertible_ToType;
    VirtualInvokeData Clone;
};
struct String__StaticFields {
    struct String *Empty;
};
struct String__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct String__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct String__VTable vtable;
};
struct Object__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Object__StaticFields {
};
struct Object__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__VTable vtable;
};
struct String___VTable {
};
struct String___StaticFields {
};
struct String___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct String___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct String___VTable vtable;
};
struct String__1__VTable {
};
struct String__1__StaticFields {
};
struct String__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct String__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct String__1__VTable vtable;
};
struct Type__1__VTable {
};
struct Type__1__StaticFields {
};
struct Type__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Type__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Type__1__VTable vtable;
};
struct Object_1 {
    struct Object_1__Class *klass;
    MonitorData *monitor;
    struct Object_1__Fields fields;
};
struct Component__Fields {
    struct Object_1__Fields _;
};
struct Component {
    struct Component__Class *klass;
    MonitorData *monitor;
    struct Component__Fields fields;
};
struct Behaviour__Fields {
    struct Component__Fields _;
};
struct Behaviour {
    struct Behaviour__Class *klass;
    MonitorData *monitor;
    struct Behaviour__Fields fields;
};
struct Camera__Fields {
    struct Behaviour__Fields _;
};
struct Camera {
    struct Camera__Class *klass;
    MonitorData *monitor;
    struct Camera__Fields fields;
};
struct Object_1__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Object_1__StaticFields {
    int32_t OffsetOfInstanceIDInCPlusPlusObject;
};
struct Object_1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object_1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object_1__VTable vtable;
};
struct Component__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Component__StaticFields {
};
struct Component__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Component__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Component__VTable vtable;
};
struct Behaviour__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Behaviour__StaticFields {
};
struct Behaviour__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Behaviour__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Behaviour__VTable vtable;
};
struct Camera__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Camera__StaticFields {
    struct Camera_CameraCallback *onPreCull;
    struct Camera_CameraCallback *onPreRender;
    struct Camera_CameraCallback *onPostRender;
};
struct Camera__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Camera__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Camera__VTable vtable;
};
struct GameObject__Fields {
    struct Object_1__Fields _;
};
struct GameObject {
    struct GameObject__Class *klass;
    MonitorData *monitor;
    struct GameObject__Fields fields;
};
struct Vector3 {
    float x;
    float y;
    float z;
};
struct Vector3__Boxed {
    struct Vector3__Class *klass;
    MonitorData *monitor;
    struct Vector3 fields;
};
struct Quaternion {
    float x;
    float y;
    float z;
    float w;
};
struct Quaternion__Boxed {
    struct Quaternion__Class *klass;
    MonitorData *monitor;
    struct Quaternion fields;
};
struct GameObject__Array {
    struct GameObject__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct GameObject *vector[32];
};
struct Vector3__Array {
    struct Vector3__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Vector3 vector[32];
};
struct Quaternion__Array {
    struct Quaternion__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Quaternion vector[32];
};
struct Transform__Fields {
    struct Component__Fields _;
};
struct Transform {
    struct Transform__Class *klass;
    MonitorData *monitor;
    struct Transform__Fields fields;
};
struct Object_1__Array {
    struct Object_1__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Object_1 *vector[32];
};
struct GameObject__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct GameObject__StaticFields {
};
struct GameObject__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameObject__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameObject__VTable vtable;
};
struct GameObject__Array__VTable {
};
struct GameObject__Array__StaticFields {
};
struct GameObject__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameObject__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameObject__Array__VTable vtable;
};
struct Vector3__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Equals_1;
    VirtualInvokeData ToString_1;
};
struct Vector3__StaticFields {
    struct Vector3 zeroVector;
    struct Vector3 oneVector;
    struct Vector3 upVector;
    struct Vector3 downVector;
    struct Vector3 leftVector;
    struct Vector3 rightVector;
    struct Vector3 forwardVector;
    struct Vector3 backVector;
    struct Vector3 positiveInfinityVector;
    struct Vector3 negativeInfinityVector;
};
struct Vector3__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Vector3__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Vector3__VTable vtable;
};
struct Vector3__Array__VTable {
};
struct Vector3__Array__StaticFields {
};
struct Vector3__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Vector3__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Vector3__Array__VTable vtable;
};
struct Quaternion__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Equals_1;
    VirtualInvokeData ToString_1;
};
struct Quaternion__StaticFields {
    struct Quaternion identityQuaternion;
};
struct Quaternion__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Quaternion__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Quaternion__VTable vtable;
};
struct Quaternion__Array__VTable {
};
struct Quaternion__Array__StaticFields {
};
struct Quaternion__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Quaternion__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Quaternion__Array__VTable vtable;
};
struct Transform__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData GetEnumerator;
};
struct Transform__StaticFields {
};
struct Transform__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Transform__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Transform__VTable vtable;
};
struct Object_1__Array__VTable {
};
struct Object_1__Array__StaticFields {
};
struct Object_1__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object_1__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object_1__Array__VTable vtable;
};
struct Light__Fields {
    struct Behaviour__Fields _;
    int32_t m_BakedIndex;
};
struct Light {
    struct Light__Class *klass;
    MonitorData *monitor;
    struct Light__Fields fields;
};
struct Light__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct Light__StaticFields {
};
struct Light__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Light__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Light__VTable vtable;
};
struct PrefabId {
    int32_t Value;
};
struct PrefabId__Boxed {
    struct PrefabId__Class *klass;
    MonitorData *monitor;
    struct PrefabId fields;
};
struct PrefabId__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct PrefabId__StaticFields {
};
struct PrefabId__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct PrefabId__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct PrefabId__VTable vtable;
};
struct UltimateCharacterLocomotion__Fields {
    struct CharacterLocomotion__Fields _;
    #if defined(_CPLUSPLUS_)
    KinematicObjectManager_UpdateLocation__Enum m_UpdateLocation;
    #else
    int32_t m_UpdateLocation;
    #endif
    struct String *m_FirstPersonStateName;
    struct String *m_ThirdPersonStateName;
    struct String *m_MovingStateName;
    struct String *m_AirborneStateName;
    struct String *m_MovementTypeFullName;
    struct String *m_FirstPersonMovementTypeFullName;
    struct String *m_ThirdPersonMovementTypeFullName;
    float m_YawMultiplier;
    struct Serialization__Array *m_MovementTypeData;
    struct Serialization__Array *m_AbilityData;
    struct Serialization__Array *m_ItemAbilityData;
    struct Serialization__Array *m_EffectData;
    struct UnityMovementTypeBoolEvent *m_OnMovementTypeActiveEvent;
    struct UnityAbilityBoolEvent *m_OnAbilityActiveEvent;
    struct UnityItemAbilityBoolEvent *m_OnItemAbilityActiveEvent;
    struct UnityBoolEvent *m_OnGroundedEvent;
    struct UnityFloatEvent *m_OnLandEvent;
    struct UnityFloatEvent *m_OnChangeTimeScaleEvent;
    struct UnityTransformEvent *m_OnChangeMovingPlatformsEvent;
    struct GameObject *m_GameObject;
    struct Animator *m_Animator;
    int32_t m_KinematicObjectIndex;
    float m_MaxHeight;
    struct Vector3 m_MaxHeightPosition;
    float m_YawAngle;
    struct Vector2 m_RawInputVector;
    bool m_Moving;
    bool m_MovingParameter;
    struct ILookSource *m_LookSource;
    struct MovementType__Array *m_MovementTypes;
    struct Ability__Array *m_Abilities;
    struct Ability__Array *m_ActiveAbilities;
    int32_t m_ActiveAbilityCount;
    bool m_DirtyAbilityParameter;
    struct MoveTowards *m_MoveTowardsAbility;
    struct ItemEquipVerifier *m_ItemEquipVerifierAbility;
    struct ItemAbility__Array *m_ItemAbilities;
    struct ItemAbility__Array *m_ActiveItemAbilities;
    int32_t m_ActiveItemAbilityCount;
    bool m_DirtyItemAbilityParameter;
    struct Effect__Array *m_Effects;
    struct Effect__Array *m_ActiveEffects;
    int32_t m_ActiveEffectCount;
    struct Dictionary_2_System_String_System_Int32_ *m_MovementTypeNameMap;
    struct MovementType *m_MovementType;
    bool m_FirstPersonPerspective;
    bool m_Alive;
    bool m_Aiming;
    struct Vector3 m_AbilityMotor;
    struct Int32__Array *m_ItemSlotStateIndex;
    struct Int32__Array *m_ItemSlotSubstateIndex;
};
struct UltimateCharacterLocomotion {
    struct UltimateCharacterLocomotion__Class *klass;
    MonitorData *monitor;
    struct UltimateCharacterLocomotion__Fields fields;
};
struct Transform__Array {
    struct Transform__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Transform *vector[32];
};
struct Attribute_1__Fields {
    struct StateObject__Fields _;
    struct String *m_Name;
    float m_MinValue;
    float m_MaxValue;
    float m_Value;
    #if defined(_CPLUSPLUS_)
    Attribute_AutoUpdateValue__Enum m_AutoUpdateValueType;
    #else
    int32_t m_AutoUpdateValueType;
    #endif
    float m_AutoUpdateStartDelay;
    float m_AutoUpdateInterval;
    float m_AutoUpdateAmount;
    struct GameObject *m_GameObject;
    float m_StartValue;
    struct ScheduledEventBase *m_AutoUpdateEvent;
};
struct Attribute_1 {
    struct Attribute_1__Class *klass;
    MonitorData *monitor;
    struct Attribute_1__Fields fields;
};
struct Transform__Array__VTable {
};
struct Transform__Array__StaticFields {
};
struct Transform__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Transform__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Transform__Array__VTable vtable;
};
struct Attribute_1__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData StateWillChange;
    VirtualInvokeData StateChange;
    VirtualInvokeData Initialize;
    VirtualInvokeData StateWillChange_1;
    VirtualInvokeData StateChange_1;
};
struct Attribute_1__StaticFields {
};
struct Attribute_1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Attribute_1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Attribute_1__VTable vtable;
};
struct UltimateCharacterLocomotion__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData StateWillChange;
    VirtualInvokeData StateChange;
    VirtualInvokeData Awake;
    VirtualInvokeData StateWillChange_1;
    VirtualInvokeData StateChange_1;
    VirtualInvokeData AddForce;
    VirtualInvokeData AddForce_1;
    VirtualInvokeData get_TimeScale;
    VirtualInvokeData set_TimeScale;
    VirtualInvokeData OnEnable;
    VirtualInvokeData Start;
    VirtualInvokeData Move;
    VirtualInvokeData UpdateUltimateLocomotion;
    VirtualInvokeData UpdateAnimator;
    VirtualInvokeData UpdatePositionAndRotation;
    VirtualInvokeData UpdateRotation;
    VirtualInvokeData ApplyRotation;
    VirtualInvokeData UpdatePosition;
    VirtualInvokeData UpdateMotorThrottle;
    VirtualInvokeData SetPlatform;
    VirtualInvokeData UpdateSlopeFactor;
    VirtualInvokeData UpdateGroundState;
    VirtualInvokeData ApplyPosition;
    VirtualInvokeData OnAnimatorMove;
    VirtualInvokeData PushRigidbody;
    VirtualInvokeData SetRotation;
    VirtualInvokeData SetPosition;
    VirtualInvokeData ResetRotationPosition;
};
struct UltimateCharacterLocomotion__StaticFields {
};
struct UltimateCharacterLocomotion__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UltimateCharacterLocomotion__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UltimateCharacterLocomotion__VTable vtable;
};
struct Vector3___VTable {
};
struct Vector3___StaticFields {
};
struct Vector3___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Vector3___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Vector3___VTable vtable;
};
struct Quaternion___VTable {
};
struct Quaternion___StaticFields {
};
struct Quaternion___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Quaternion___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Quaternion___VTable vtable;
};
struct Survival__Fields {
    struct GlobalEventListener__Fields _;
    struct String *_sceneName_k__BackingField;
    struct Dictionary_2_UnityEngine_GameObject_NolanPerkController_ *m_PlayerPerkControllers;
    struct List_1_NolanBehaviour_ *m_PlayerNolanBehaviours;
    struct Transform *m_AzazelSpawnPoint;
    struct Transform *m_DemoAnnaTeleportSpawnPoint;
    struct Transform *m_CageSpawnPoint;
    struct Transform *m_AtticCageSpawnPoint;
    float m_CrawlerSpawnRadiusToCheckForOtherCrawlers;
    float m_CrawlerSpawnRadiusToCheckForOtherPlayers;
    float m_FoodSourceSpawnRadiusToCheckForOtherPlayers;
    float m_FoodSourceSpawnRadiusToCheckForOtherFoodSources;
    struct Int32__Array *m_DemonSpawnCounts;
    struct Int32__Array *m_EasyDemonSpawnCounts;
    int32_t m_MinHayInWorld;
    int32_t m_NumHayToRespawn;
    int32_t m_MinFirstAidsInWorld;
    int32_t m_NumFirstAidsToRespawn;
    int32_t firstAidsSpawned;
    int32_t m_MinBatteriesInWorldNormal;
    int32_t m_MinBatteriesInWorldHard;
    int32_t m_MinBatteriesInWorldNightmare;
    int32_t m_NumBatteriesToRespawn;
    int32_t m_MinBleachInWorld;
    int32_t m_NumBleachToRespawn;
    int32_t m_MinMatchboxInWorld;
    int32_t m_NumMatchboxToRespawn;
    bool spawnedFinalGoats;
    bool spawnedFirstDemon;
    struct GameObject__Array *gasolineSpawnPoints;
    struct GameObject__Array *goatSpawnPoints;
    struct GameObject__Array *firstAidSpawnPoints;
    struct GameObject__Array *initialGoatSpawnPoints;
    struct GameObject__Array *keySpawnPoints;
    struct GameObject__Array *demonSpawnPoints;
    struct GameObject__Array *fuseSpawnPoints;
    struct GameObject__Array *batterySpawnPoints;
    struct GameObject__Array *bleachSpawnPoints;
    struct GameObject__Array *matchboxSpawnPoints;
    struct GameObject__Array *randomInitialGoatSpawnPoints;
    struct TrashCanSpawnPoint__Array *finalRatsTrashCanSpawnPoints;
    struct GameObject__Array *foodSourceSpawnPoints;
    struct GameObject__Array *foodSpawnPoints;
    struct SurvivalObjectBurnController *m_BurnController;
    struct MapController *m_MapController;
    struct Camera *m_mainCamera;
    struct Light *m_EmoteLight;
    struct AnnaIntro *m_AnnaIntro;
    struct MollyIntro *m_MollyIntro;
    struct InnIntro *m_InnIntro;
    struct TownIntro *m_TownIntro;
    struct SlaughterhouseIntro *m_SlaughterhouseIntro;
    struct ManorIntro *m_ManorIntro;
    struct Transform *centreOfHouse;
    struct GameObject *m_astarPath;
    struct GameObject *nightmarePrefab;
    struct PlayableDirector *m_IntroDirector;
    struct GameUI *gameUI;
    struct MainSceneToken *mainSceneToken;
    struct InnMapController *m_InnMapController;
    struct ManorMapController *m_ManorMapController;
    struct GameObject *m_Azazel;
    struct PrefabId m_AzazelPrefabId;
    struct SurvivalVR *survivalVR;
    struct FreezerRoomController *m_FreezerRoomController;
    bool enableWeather;
    struct GameObject *thunderLightning;
    int32_t rainThreshold;
    int32_t heavierRainThreshold;
    int32_t thunderLightningThreshold;
    struct WindZone *windZone;
    float m_WindLightMultiplier;
    float m_WindHeavyMultiplier;
    float m_WindThunderMultiplier;
    struct Color m_SkyFinalTint;
    struct Color m_SkyOriginalTint;
    struct Material *m_SkyMaterial;
    bool enableRain;
    struct Coroutine *darkenSkyCoroutine;
    struct OutdoorsAudio *m_outdoorsAudio;
    bool m_debugFlashlightRays;
    float gameOverDemoVideoDelay;
    float gameOverEndingDelay;
    struct GameObject *m_failEnding1;
    struct GameObject *m_failEnding2;
    struct Camera *m_endingCamera;
    struct Light *m_endingLight;
    struct GameObject *m_failEnding3;
    struct GameObject *m_failEnding3Armchair;
    struct DoorBehaviour__Array *m_failEnding3DoorsToOpen;
    struct GameObject *m_winEnding;
    struct AudioSource *m_finalScreamAudioSource;
    struct GameObject *m_innCaveEndingPumpkin;
    struct GameObject *m_innCaveSpiderDestination;
    struct GameObject__Array *m_AsylumMorgueEndingObjectsToDisable;
    struct List_1_UnityEngine_GameObject_ *m_droppedHay;
    struct List_1_UnityEngine_GameObject_ *m_eggs;
    struct List_1_UnityEngine_GameObject_ *m_activeRitualBooks;
    struct List_1_UnityEngine_GameObject_ *m_cleanHeads;
    struct GameObject *m_devourLogo;
    bool m_enableEndingsInDebug;
    bool m_forceFourPlayersInEndings;
    struct GameObject *m_winEndingDialogue;
    struct JumpScare__Array *azazelJumpScares;
    struct JumpScare__Array *crawlerJumpScares;
    struct JumpScare__Array *hidingJumpScares;
    struct AudioSource *jumpScareAudioSource;
    struct GameObject__Array *cutsceneCharacterPrefabs;
    struct GameObject__Array *m_TownSaloonEndingObjectsToDisable;
    struct GameObject__Array *m_WinEndingObjectsToDisable;
    struct GameObject__Array *m_SlaughterhouseKitchenEndingObjectsToDisable;
    struct GameObject__Array *m_SlaughterhouseDunkerEndingObjectsToDisable;
    struct GameObject__Array *m_SlaughterhouseBoarEndingObjectsToDisable;
    struct MeshRenderer__Array *m_TownChurchMeshRenderers;
    struct EndingTextController *m_EndingTextController;
    struct GameObject *m_AsylumMorgueEndingSilhouette;
    bool m_startingToPlayFailEnding;
    struct Color m_endingLightOriginalColor;
    float m_endingLightOriginalRange;
    float m_endingLightOriginalIntensity;
    bool m_endingPlaying;
    struct GameObject *m_activeEnding;
    struct GameObject__Array *m_ManorGraveEndingObjectsToDisable;
    struct GameObject *m_EndingWeather;
    struct GameObject__Array *m_ManorWinEndingObjectsToDisable;
    struct GameObject *m_collectableRoses;
    struct GameObject *m_collectablePumpkins;
    struct GameObject *m_collectablePresents;
    struct String *wonSPAchName;
    struct String *wonCoopAchName;
    struct String *wonHardAchName;
    struct String *wonHardSPAchName;
    struct String *wonNightmareAchName;
    struct String *wonNightmareSPAchName;
    struct GameObject *m_frontPorchRain;
    struct GameObject *m_backPorchRain;
    struct ParticleSystem *m_innCaveDustParticleSystem;
    struct BoxCollider__Array *m_townSaloonDoorColliders;
    struct CrowPerchManager *m_crowPerchManager;
    struct ManorDeadRealmTrigger *ManorDeadRealmTrigger;
    struct Vent__Array *m_vents;
    struct CustomPostProcessing *customPostProcessing;
    struct DoorGraphUpdate *m_cageDoorGraphUpdate;
    struct Transform *m_cageDoorHolder;
    struct Animator *m_cageAnimator;
    struct RuntimeAnimatorController *m_IKPassThroughController;
    float showDropTutorialOnGoatFleeDelay;
    struct GameObject *heightFog;
    struct HeightFogTrigger *heightFogTrigger;
    struct GameObject *heightFogGlobal;
    struct GameObject *HeightFogIndoorsOverride;
    bool disableGlobalHeightFogOnKnockout;
    struct AudioEffectsManager *audioEffectsManager;
    float m_flashlightRaycastFrequencyInFrames;
    struct List_1_Outline_ *m_Outlines;
    bool m_knockoutOccurred;
    bool m_knockoutOnThisPlayerOccurred;
    bool m_knockoutOnAnotherPlayer;
    #if defined(_CPLUSPLUS_)
    DevourGameMode__Enum m_devourGameMode;
    #else
    int32_t m_devourGameMode;
    #endif
    bool m_batteries;
    bool m_medkits;
    bool goatHasBeenPickedUp;
    float showDropTutorialOnGoatFleeTime;
    bool showDropTutorialOnGoatFlee;
    struct PrefabId animalPrefabId;
    struct PrefabId crawlerPrefabId;
    struct PrefabId foodSourcePrefabId;
    struct DoorBehaviour__Array *doorBehaviours;
    struct NNConstraint *lockedOrClosedTagsOnly;
    struct Coroutine *checkGraphTagsCoroutine;
    int32_t currentNumObjectsBurned;
    float latestBurnTime;
    struct List_1_HidingSpotController_ *hidingSpots;
    int32_t crawlLayer;
    struct VoiceReverbTrigger__Array *voiceReverbTriggers;
    int32_t connectionTimeout;
    bool canEnablePetAI;
    float m_WindMainLightWeather;
    float m_WindMainHeavyWeather;
    float m_WindMainThunderWeather;
    float m_WindTurbulenceLightWeather;
    float m_WindTurbulenceHeavyWeather;
    float m_WindTurbulenceThunderWeather;
};
struct Survival {
    struct Survival__Class *klass;
    MonitorData *monitor;
    struct Survival__Fields fields;
};
struct SurvivalLobbyController__Fields {
    struct EntityEventListener_1_ISurvivalLobbyState___Fields _;
    struct GameObject__Array *survivalPrefabs;
    struct Transform__Array *survivalPlayerLobbyPositions;
    struct SurvivalLobbyControllerCanvas__Array *survivalPlayerCanvases;
    struct GameObject__Array *survivalPlayerSpotlights;
    struct Transform *singlePlayerLobbyPosition;
    struct SurvivalLobbyControllerCanvas *singlePlayerCanvas;
    struct GameObject *singlePlayerSpotlight;
    struct GameObject__Array *speakingImageGameObjects;
    struct Image__Array *speakingImages;
    struct AudioSource *onCharacterLoadedAudioSource;
    float speakingTolerance;
    struct GameObject *blockerPrefab;
    int32_t survivalPlayerIndex;
    struct GameObject *_m_SurvivalPlayer_k__BackingField;
    struct Coroutine *m_SurvivalPlayerRendererCoroutine;
    struct Menu *m_Menu;
    bool m_ready;
    struct String__Array *m_Robes;
    struct String *m_selectedRobe;
    struct DissonanceComms *dissonanceComms;
    struct RewiredVoiceBroadcastTrigger *voiceBroadcastTriggger;
    struct Image *localSpeaking;
    struct VoicePlayerState *localVoicePlayerState;
    struct Dictionary_2_System_Int32_Dissonance_VoicePlayerState_ *playersSpeaking;
    struct AsyncOperationHandle_1_UnityEngine_Material_ matHandle;
    struct GameObject *blocker;
    int32_t playerDropdownOpenIndex;
    float lastSpeakingAlphaCheckTime;
    float lobbyStartTime;
};
struct SurvivalLobbyController {
    struct SurvivalLobbyController__Class *klass;
    MonitorData *monitor;
    struct SurvivalLobbyController__Fields fields;
};
struct Menu__Fields {
    struct GlobalEventListener__Fields _;
    struct Text *titleText;
    struct AudioSource *backgroundMusic;
    struct PostProcessVolume *postProcessVolume;
    struct GameObject *mainCamera;
    struct Camera *uiCamera;
    struct String *mainSceneName;
    struct Text *currentRegionLabel;
    struct Texture2D *cursorTextureOSX;
    struct GameObject *iconButtons;
    struct GameObject *uiCanvas;
    struct ServerBrowser *serverBrowser;
    struct CanvasScaler *menuCanvasScaler;
    struct GameObject *rewiredEventSystem;
    struct GameObject *embers;
    struct GameObject *versionLabel;
    struct GameObject *splashScreens;
    struct RectTransform *sbgRectTransform;
    struct RectTransform *photoSensitivityRectTransform;
    struct Image *splashBackground;
    struct CanvasGroup *loadingCanvasGroup;
    struct CanvasGroup *mainMenuCanvasGroup;
    struct CanvasGroup *hostCanvasGroup;
    struct CanvasGroup *serverBrowserCanvasGroup;
    struct CanvasGroup *lobbyCanvasGroup;
    struct CanvasGroup *optionsCanvasGroup;
    struct CanvasGroup *messageModalCanvasGroup;
    struct CanvasGroup *confirmModalCanvasGroup;
    struct CanvasGroup *graphicsSettingsCanvasGroup;
    struct CanvasGroup *languageCanvasGroup;
    struct CanvasGroup *keyBindingsCanvasGroup;
    struct CanvasGroup *gameplayCanvasGroup;
    struct CanvasGroup *audioSettingsCanvasGroup;
    struct CanvasGroup *joinDiscordMessageModalCanvasGroup;
    struct CanvasGroup *creditsCanvasGroup;
    struct CanvasGroup *globalBackgroundCanvasGroup;
    struct CanvasGroup *reconnectModalCanvasGroup;
    struct CanvasGroup *vrSettingsCanvasGroup;
    struct CanvasGroup *vrControlsCanvasGroup;
    struct CanvasGroup *statsCanvasGroup;
    struct CanvasGroup *challengesCanvasGroup;
    struct CanvasGroup *iconButtonsCanvasGroup;
    struct CanvasGroup *lobbyIconButtonsCanvasGroup;
    struct Canvas *splashCanvas;
    struct RectTransform *splashCanvasRT;
    struct Canvas *loadingCanvas;
    struct RectTransform *loadingCanvasRT;
    struct Canvas *menuCanvas;
    struct RectTransform *menuCanvasRT;
    struct Button *newsButton;
    struct Button *soloModeButton;
    struct Button *hostButton;
    struct Button *joinButton;
    struct Button *optionsButton;
    struct Button *quitButton;
    struct Button *creditsButton;
    struct Button *showChallengesScreenButton;
    struct Image *showChallengesScreenButtonPip;
    struct Image *showChallengesScreenGlyph;
    struct Button *bgmSwitcherButton;
    struct Button *hostStartButton;
    struct Button *hostBackButton;
    struct TMP_InputField *hostServerPasswordInputField;
    struct Button *hostServerPasswordVisibilityButton;
    struct Toggle *hostPrivateServer;
    struct Dropdown *hostRegion;
    struct GameObject *hostServerNameWrapper;
    struct Text *hostServerNameLabel;
    struct Image *hostServerPrivateLockIcon;
    struct Button *hostServerPasswordButton;
    struct RectTransform *hostButtonsRT;
    struct RectTransform *hostBackgroundRT;
    struct Image *hostBackground;
    struct VerticalLayoutGroup *hostPanelLayoutGroup;
    struct UITextColorController *hostServerPasswordTextColorController;
    struct Image *hostServerPasswordVisibilityImage;
    struct Sprite *hostServerPasswordVisibleSprite;
    struct Sprite *hostServerPasswordHiddenSprite;
    struct OutfitPreviewLoader *outfitPreviewLoader;
    struct GameObject *steamInventoryValidatorPrefab;
    struct GameObject *survivalLobbyControllerPrefab;
    struct DissonanceComms *dissonanceComms;
    struct CanvasGroup *lobbyOptionsCanvasGroup;
    struct Image *lobbyOptionsBackground;
    struct GameObject *lobbyOutfitTabs;
    struct CanvasGroup *lobbyOutfitCanvasGroup;
    struct CanvasGroup *lobbyFlashlightCanvasGroup;
    struct CanvasGroup *lobbyPerksSharedControlsCanvasGroup;
    struct CanvasGroup *lobbyPerksGroup1CanvasGroup;
    struct CanvasGroup *lobbyPerksGroup2CanvasGroup;
    struct CanvasGroup *lobbyPetsCanvasGroup;
    struct CanvasGroup *lobbyEmotesCanvasGroup;
    struct CanvasGroup *lobbyMapsCanvasGroup;
    struct Button *lobbyOutfitTab;
    struct Button *lobbyFlashlightTab;
    struct Button *lobbyPetsTab;
    struct Button *lobbyEmotesTab;
    struct Color lobbyTabActiveColor;
    struct Color lobbyTabInactiveColor;
    struct Color lobbyTabActiveIconColor;
    struct Color lobbyTabInactiveIconColor;
    struct RectTransform *lobbyCanvasRect;
    struct Button *lobbyInviteButton;
    struct Button *lobbyStartButton;
    struct RectTransform *lobbyStartButtonTextRect;
    struct Button *lobbyProximityVoiceNextButton;
    struct Button *lobbyProximityVoicePrevButton;
    struct Button *lobbyPlayerCollisionsNextButton;
    struct Button *lobbyPlayerCollisionsPrevButton;
    struct Button *lobbyBatteriesNextButton;
    struct Button *lobbyBatteriesPrevButton;
    struct Button *lobbyMedkitsNextButton;
    struct Button *lobbyMedkitsPrevButton;
    struct Text *lobbyProximityVoiceStateText;
    struct Text *lobbyPlayerCollisionsStateText;
    struct Text *lobbyBatteriersStateText;
    struct Text *lobbyMedkitsStateText;
    struct Image *lobbyDifficultyEXPBonus;
    struct Text *lobbyDifficultEXPBonusTooltipText;
    struct Image *lobbyBatteriesEXPBonus;
    struct Text *lobbyBatteriesEXPBonusTooltipText;
    struct Image *lobbyMedkitsEXPBonus;
    struct Text *lobbyMedkitsEXPBonusTooltipText;
    struct Button *lobbyChangeCharacterPrevButton;
    struct Button *lobbyChangeCharacterNextButton;
    struct Text *lobbyChangeCharacterName;
    struct Image *lobbyChangeCharacterImage;
    struct Text *lobbyChangeCharacterDescription;
    struct AudioSource *swapMapAudioSource;
    struct GameObject *lobbyChangeCharacterBlocked;
    struct Button *lobbyExitButton;
    struct Text *lobbyWaitingForHostToStart;
    struct Toggle *lobbyVoiceChat;
    struct Button *lobbyReady;
    struct Text *lobbyReadyText;
    struct RectTransform *lobbyReadyButtonTextRect;
    struct GameObject *lobbyGameMode;
    struct Text *lobbyGameModeLabel;
    struct Button *lobbyGameModeNextButton;
    struct Button *lobbyGameModePrevButton;
    struct GameObject *lobbyMap;
    struct Text *lobbyMapLabel;
    struct Text *lobbyMapHeaderLabel;
    struct GameObject *robeSelectionPrefab;
    struct Transform *robeViewContent;
    struct Text *robeViewTooltip;
    struct Text *pingText;
    struct Text *serverNameText;
    struct GameObject *lobbyPrivateServerIcon;
    struct Selectable *lobbyCharacterSelectable;
    struct Button *lobbyMapButton;
    struct Selectable *lobbyDifficultySelectable;
    struct Selectable *lobbyBatteriesSelectable;
    struct Selectable *lobbyProximityVoiceSelectable;
    struct Selectable *lobbyPlayerCollisionsSelectable;
    struct Selectable *lobbyMedkitsSelectable;
    struct GameObject__Array *lobbyOptionsItems;
    struct ScrollRect *outfitViewScrollRect;
    struct GameObject *outfitSelectionPrefab;
    struct Button *outfitButton;
    struct Button *outfitBackButton;
    struct Text *outfitCharacterNameLabel;
    struct Text *outfitTitle;
    struct Text *outfitUnlockDescLabel;
    struct Button *outfitBuyButton;
    struct GameObject__Array *objectsToHideInOutfitMode;
    struct Transform *lobbyCameraPos;
    struct Transform *mainMenuCameraPos;
    struct GameObject *modeLocked;
    struct GameObject *modeLockedFiller;
    struct GameObject *readyLocked;
    struct GameObject *readyLockedFiller;
    struct Button *perksButton;
    struct Button *perksBackButton;
    struct GameObject *lobbyPerksTabs;
    struct Button *perksGroup1Tab;
    struct Button *perksGroup2Tab;
    struct Transform *perksGroup1ViewContent;
    struct Transform *perksGroup2ViewContent;
    struct ScrollRect *perksGroup1ViewScrollRect;
    struct ScrollRect *perksGroup2ViewScrollRect;
    struct Text *perkViewNameTooltip;
    struct Text *perkViewDescTooltip;
    struct GameObject *perkSelectionPrefab;
    struct Text *perkCurrentTokensText;
    struct Button *showStatsScreenButton;
    struct Button *lobbyOptionsButton;
    struct ScrollRect *flashlightViewScrollRect;
    struct Transform *flashlightViewContent;
    struct Text *flashlightViewNameTooltip;
    struct GameObject *flashlightSelectionPrefab;
    struct Button *flashlightBackButton;
    struct Text *flashlightCurrentTokensText;
    struct Button *flashlightUnlockButton;
    struct Text *flashlightUnlockButtonText;
    struct Image *lobbyRightHandSideGlyph;
    struct Image *lobbyLeftHandSideGlyph;
    struct Image *outfitsPrevTabGlyph;
    struct Image *outfitsNextTabGlyph;
    struct Button *loadSceneSyncButton;
    struct ScrollRect *petViewScrollRect;
    struct Button *petsBackButton;
    struct Transform *petsViewContent;
    struct Button *petBuyButton;
    struct GameObject *petSelectionPrefab;
    struct Text *petViewNameTooltip;
    struct ScrollRect *emotesViewScrollRect;
    struct Button *emotesBackButton;
    struct Transform *emotesViewContent;
    struct GameObject *emoteSelectionPrefab;
    struct Text *emoteViewNameTooltip;
    struct Text *emotesCurrentTokensText;
    struct Button *emoteUnlockButton;
    struct Text *emoteUnlockButtonText;
    struct Transform *mapsViewContent;
    struct GameObject *mapSelectionPrefab;
    struct List_1_Horror_Menu_MapListItem_ *maps;
    struct Button *mapsBackButton;
    struct GameObject *mapsNextArrow;
    struct Sprite *redThumbBackgroundSprite;
    struct Sprite *blackThumbBackgroundSprite;
    struct Sprite *blueThumbBackgroundSprite;
    struct MenuCredits *credits;
    struct Button *creditsExitButton;
    struct Text *messageModalText;
    struct Button *messageModalOKButton;
    struct Text *confirmModalText;
    struct Button *confirmModalYesButton;
    struct Button *confirmModalNoButton;
    struct Text *joinDiscordMessageModalText;
    struct Button *joinDiscordMessageModalOKButton;
    struct CanvasGroup *unlockedModalCanvasGroup;
    struct Text *unlockedModalText;
    struct Button *unlockedModalOKButton;
    struct Image *unlockedModalImage;
    struct CanvasGroup *perkModalCanvasGroup;
    struct Text *perkModalText;
    struct Text *perkModalDescText;
    struct Text *perkModalCostText;
    struct Button *perkModalUnlockButton;
    struct Button *perkModalCancelButton;
    struct CanvasGroup *flashlightModalCanvasGroup;
    struct Text *flashlightModalText;
    struct Text *flashlightModalCostText;
    struct Button *flashlightModalUnlockButton;
    struct Button *flashlightModalCancelButton;
    struct CanvasGroup *emoteModalCanvasGroup;
    struct Text *emoteModalText;
    struct Text *emoteModalCostText;
    struct Button *emoteModalUnlockButton;
    struct Button *emoteModalCancelButton;
    struct String *betaWelcomeMessageText;
    struct Button *reconnectModalYesButton;
    struct Button *reconnectModalNoButton;
    struct CanvasGroup *publicServerWarningModalCanvasGroup;
    struct Toggle *publicServerWarningDontShowAgain;
    struct Button *publicServerWarningModalYesButton;
    struct Button *publicServerWarningModalNoButton;
    struct Button *serverBrowserBackButton;
    struct Dropdown *serverBrowserRegionPicker;
    struct Text *playerCountText;
    struct Button *optionsBackButton;
    struct Button *gameplayButton;
    struct Button *audioSettingsButton;
    struct Button *graphicsSettingsButton;
    struct Button *keyBindingsButton;
    struct Button *vrSettingsButton;
    struct Button *statsButton;
    struct Button *gameplayBackButton;
    struct Slider *sensitivitySlider;
    struct TMP_InputField *sensitivityInputField;
    struct Slider *fovSlider;
    struct Text *fovText;
    struct Button *invertMouseButton;
    struct LocalizationTextToggle *invertMouseState;
    struct Dropdown *crouchModeDropdown;
    struct Dropdown *sprintModeDropdown;
    struct Button *languageButton;
    struct Button *inputModeButton;
    struct LocalizationTextToggle *inputModeState;
    struct Button *inputAccelerationButton;
    struct LocalizationTextToggle *inputAccelerationState;
    struct Button *profanityFilteringButton;
    struct LocalizationTextToggle *profanityFilteringState;
    struct Button *arachnophobiaModeButton;
    struct LocalizationTextToggle *arachnophobiaModeState;
    struct Button *headbobButton;
    struct LocalizationTextToggle *headbobState;
    struct Button *gamepadRumbleButton;
    struct LocalizationTextToggle *gamepadRumbleState;
    struct Button *vrHandTrackingButton;
    struct LocalizationTextToggle *vrHandTrackingState;
    struct Button *tutorialsButton;
    struct LocalizationTextToggle *tutorialsState;
    struct Button *bloodEffectsButton;
    struct LocalizationTextToggle *bloodEffectsState;
    struct Button *ventAutoCrouchButton;
    struct LocalizationTextToggle *ventAutoCrouchState;
    struct Button *classicEmotesButton;
    struct LocalizationTextToggle *classicEmotesState;
    struct Button *nameplatesButton;
    struct LocalizationTextToggle *nameplatesState;
    struct AudioMixer *mixer;
    struct Button *audioSettingsBackButton;
    struct Slider *masterVolumeSlider;
    struct Slider *musicVolumeSlider;
    struct Slider *effectsVolumeSlider;
    struct Slider *voiceVolumeSlider;
    struct Button *voiceChatButton;
    struct LocalizationTextToggle *voiceChatState;
    struct Dropdown *voiceInputMode;
    struct Dropdown *voiceInputDevice;
    struct Button *muteJumpScaresButton;
    struct LocalizationTextToggle *muteJumpScaresState;
    struct Button *graphicsSettingsBackButton;
    struct Button *qualityIncreaseButton;
    struct Button *qualityDecreaseButton;
    struct Text *currentQuality;
    struct Button *vsyncButton;
    struct LocalizationTextToggle *vsyncState;
    struct Dropdown *antialiasingDropdown;
    struct Button *motionBlurButton;
    struct LocalizationTextToggle *motionBlurState;
    struct Button *hdFlashlightButton;
    struct LocalizationTextToggle *hdFlashlightState;
    struct GameObject *hdFlashlightWarning;
    struct Button *resolutionButton;
    struct Text *currentResolutionText;
    struct Dropdown *displayMonitorDropdown;
    struct Button *displayMonitorButton;
    struct Button *fpsLimitButton;
    struct Dropdown *fpsLimitDropdown;
    struct Toggle *englishButton;
    struct Toggle *japaneseButton;
    struct Toggle *chineseButton;
    struct Toggle *russianButton;
    struct Toggle *polishButton;
    struct Toggle *germanButton;
    struct Toggle *spanishSpainButton;
    struct Toggle *frenchButton;
    struct Toggle *italianButton;
    struct Toggle *koreanButton;
    struct Toggle *turkishButton;
    struct Toggle *spanishLatinButton;
    struct Toggle *portugueseButton;
    struct Toggle *portugueseBrButton;
    struct Toggle *thaiButton;
    struct Toggle *czechButton;
    struct Toggle *hungarianButton;
    struct Toggle *romanianButton;
    struct Toggle *slovakButton;
    struct Toggle *dutchButton;
    struct Toggle *norwegianButton;
    struct Toggle *indonesianButton;
    struct Toggle *finnishButton;
    struct Toggle *ukrainianButton;
    struct Button *languageBackButton;
    struct Button *languageOKButton;
    int32_t selectedLanguage;
    struct CanvasGroup *resolutionModal;
    struct GameObject *resolutionModalContent;
    struct Button *resolutionModalClose;
    struct Button *resolutionModalApply;
    struct Dropdown *displayModeDropdown;
    struct GameObject *altTabWarning;
    struct Button *keyBindingsBackButton;
    struct Button *keyBindingsResetButton;
    struct Toggle *keyBindingsKeyboardTab;
    struct Toggle *keyBindingsGamepadTab;
    struct Transform *keyBindingsContent;
    struct UIScrollToSelection *keyBindingsScrollToSelection;
    struct ScrollRect *keyBindingsScrollRect;
    struct GameObject *textChat;
    struct TMP_InputField *textChatInput;
    struct GameObject *messagePrefab;
    bool canSubmitTextInput;
    struct Transform *messageListContent;
    struct GameObject *textChatMessages;
    struct Button *textChatSendButton;
    struct GameObject *farmhouse;
    struct GameObject *asylum;
    struct GameObject *inn;
    struct GameObject *town;
    struct GameObject *slaughterhouse;
    struct GameObject *manor;
    struct GameObject *mainMenuEnvironment;
    struct GameObject *mainMenuHeightFog;
    struct AudioSource *hoverAudioSource;
    struct AudioSource *clickAudioSource;
    struct AudioSource *startGameAudioSource;
    struct Button *vrSettingsBackButton;
    struct Button *vrSeatedModeButton;
    struct LocalizationTextToggle *vrSeatedModeState;
    struct Button *vrLeftHandedModeButton;
    struct LocalizationTextToggle *vrLeftHandedModeState;
    struct Button *vrTurningAnglePrevButton;
    struct Text *vrTurningAngleState;
    struct Button *vrTurningAngleNextButton;
    struct Button *vrMovementDirectionPrevButton;
    struct Text *vrMovementDirectionState;
    struct Button *vrMovementDirectionNextButton;
    struct Button *vrTurningModePrevButton;
    struct Text *vrTurningModeState;
    struct Button *vrTurningModeNextButton;
    struct Slider *vrTurningSpeedSlider;
    struct TMP_InputField *vrTurningSpeedInput;
    struct Dropdown *vrSprintModeDropdown;
    struct Button *vrHTCViveTrackpadClickMode;
    struct LocalizationTextToggle *vrHTCViveTrackpadClickModeState;
    struct Button *vrControlsButton;
    struct Button *vrControlsBackButton;
    struct GameObject *vrControlsOculusTouch;
    struct GameObject *vrControlsVive;
    struct GameObject *vrControlsValveIndex;
    struct Button *vrGamepadModeButton;
    struct LocalizationTextToggle *vrGamepadModeState;
    struct GameObject *vrWMRControllerType;
    struct Dropdown *vrWMRControllerTypeDropdown;
    struct Button *statsBackButton;
    struct PlayerStatsController *playerStatsController;
    struct Button *challengesBackButton;
    struct Menu_ChallengeContainer__Array *challengeContainers;
    struct Text *challengesResetCountdown;
    struct MenuBGMSwitcher *bgmSwitcher;
    struct CanvasGroup *gameStatsCanvasGroup;
    struct Text *gameStatsPlayerRankNames;
    struct RectTransform *gameStatsPlayerRankNamesRt;
    struct Image *rankIcon;
    struct Text *rankNumber;
    struct Image *rankExpCircleBg;
    struct Image *rankExpCircle;
    struct Text *rankExpNumber;
    struct Text *expInfoText;
    struct Text *gameStatsMapNameText;
    struct Text *gameStatsTimePlayedText;
    struct Image *gameStatsRitualProgressIcon;
    struct Text *gameStatsRitualProgressText;
    struct GameObject *awardsParent;
    struct Menu_GameStatsAward__Array *gameStatsAwards;
    struct AudioSource *expAudioSource;
    struct AudioSource *tokensPerksAudioSource;
    struct AudioClip *expTickAudioClip;
    struct AudioClip *tokenUnlockAudioClip;
    struct AudioClip *perkUnlockAudioClip;
    struct Button *gameStatsCloseButton;
    struct Button *gameStatsStatsButton;
    struct Sprite *goatIcon;
    struct Sprite *ratIcon;
    struct Sprite *eggIcon;
    struct Sprite *bookIcon;
    struct Sprite *pigIcon;
    struct Sprite *headIcon;
    struct Sprite *tokenSprite;
    struct GameObject *vrPivot;
    struct TrackedPoseDriver *vrTrackedPoseDriver;
    struct GameObject *vrEventSystem;
    struct Camera *vrUICamera;
    struct Transform *vrCameraTransform;
    struct VRUIPointer *vrLeftHandUIPointer;
    struct VRUIPointer *vrRightHandUIPointer;
    struct GameObject *vrSplash360Background;
    struct VRUIActions *vrUIActions;
    struct GameObject *vrRecenterCanvas;
    struct Image *vrRecenterIcon;
    struct GameObject *vrOutfitPreview360Background;
    struct VRHandAdjustment *vrLeftHandAdjustment;
    struct VRHandAdjustment *vrRightHandAdjustment;
    struct GameObject *vrBlockRaycasts;
    struct GameObject *vrLobbyBlockRaycasts;
    struct Transform *vrLeftHand;
    struct Transform *vrRightHand;
    struct VRUserPresence *vrUserPresence;
    struct GameObject__Array *vrMainMenuEnvironmentObjectsToDisable;
    struct GlobalNonNativeKeyboard *vrKeyboard;
    struct TrackedDeviceRaycaster *vrKeyboardTrackedDeviceRaycaster;
    struct TrackedDeviceRaycaster *menuCanvasTrackedDeviceRaycaster;
    struct PlayRandomAudioClip *halloweenIntroAudio;
    struct PlayRandomAudioClip *christmasIntroAudio;
    int32_t mollyHardEmoteItemDefId;
    int32_t samHardEmoteItemDefId;
    int32_t nathanHardEmoteItemDefId;
    int32_t zaraHardEmoteItemDefId;
    int32_t cultistHardEmoteItemDefId;
    int32_t aprilHardEmoteItemDefId;
    int32_t annaHardEmoteItemDefId;
    int32_t kaiHardEmoteItemDefId;
    int32_t frankHardEmoteItemDefId;
    struct CanvasGroup *newsGroup;
    struct Transform *newsContent;
    struct GameObject *newsItemPrefab;
    bool debugDisableSceneSyncOnClient;
    struct Callback_1_LobbyCreated_t_ *m_SteamLobbyCreated;
    struct Callback_1_GameLobbyJoinRequested_t_ *m_SteamLobbyJoinRequested;
    struct Callback_1_LobbyEnter_t_ *m_SteamLobbyEnter;
    struct Callback_1_FloatingGamepadTextInputDismissed_t_ *m_FloatingGamepadTextInputDismissed;
    struct Callback_1_GamepadTextInputDismissed_t_ *m_GamepadTextInputDismissed;
    struct CSteamID steamLobbyID;
    struct String *steamName;
    struct String *steamID;
    struct HAuthTicket steamAuthTicket;
    bool soloMode;
    #if defined(_CPLUSPLUS_)
    GameMode__Enum gameMode;
    #else
    int32_t gameMode;
    #endif
    struct String *boltSessionID;
    struct CSteamID hostSteamID;
    struct String *steamInvitePassword;
    #if defined(_CPLUSPLUS_)
    DevourGameMode__Enum devourGameMode;
    #else
    int32_t devourGameMode;
    #endif
    #if defined(_CPLUSPLUS_)
    DevourMap__Enum devourMap;
    #else
    int32_t devourMap;
    #endif
    struct PhotonRoomProperties *roomProperties;
    struct RoomProtocolToken *roomToken;
    bool sessionCreated;
    bool gameStarted;
    struct DepthOfField *depthOfField;
    struct Grain *grain;
    bool changingRegion;
    #if defined(_CPLUSPLUS_)
    PhotonRegion_Regions__Enum regionToChangeTo;
    #else
    int32_t regionToChangeTo;
    #endif
    bool isReady;
    struct String__Array *args;
    bool unlockedModalLock;
    bool lobbyProximityVoice;
    bool lobbyPlayerCollisions;
    bool lobbyBatteries;
    bool lobbyMedkits;
    struct SurvivalLobbyController *survivalLobbyController;
    int32_t perkUnlockSteamItemDefId;
    int32_t flashlightUnlockSteamItemDefId;
    int32_t emoteUnlockSteamItemDefId;
    bool lobbyOptionsOpen;
    bool messageModalLock;
    bool lobbyPlayerDropdownsSelected;
    int32_t activePerksTab;
    struct GameObject *lobbyLastSelection;
    bool reconnecting;
    float lastMessageSentTime;
    bool clearCollectables;
    bool clearCollectablesForMap;
    struct String *clearCollectablesMap;
    bool steamConnectLobbyArg;
    bool statsOpenedFromPostGameStats;
    float originalLobbyCameraPosZ;
    bool isDropdownOpen;
    struct SteamInventoryValidator *steamInventoryValidator;
    bool vrMode;
    bool vrInitialRotationSet;
    struct Transform *vrLastCameraPosSet;
    float _vrCameraCenterY_k__BackingField;
    bool vrCancelPressed;
    float vrCancelPressedTime;
    bool vrMenuRecenterTriggered;
    struct String *vrActiveRuntime;
    bool _vrIsUsingViveControllers_k__BackingField;
    #if defined(_CPLUSPLUS_)
    InputDeviceCharacteristics__Enum vrHandControllerCharacteristics;
    #else
    uint32_t vrHandControllerCharacteristics;
    #endif
    float LOBBY_CAMERA_LOW_ASPECT_RATIO_Z;
    struct PhotonRegion_Regions__Enum__Array *BlacklistedRegions;
};
struct Menu {
    struct Menu__Class *klass;
    MonitorData *monitor;
    struct Menu__Fields fields;
};
struct GameConfigToken {
    struct GameConfigToken__Class *klass;
    MonitorData *monitor;
    struct GameConfigToken__Fields fields;
};
struct Attribute_1__Array {
    struct Attribute_1__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Attribute_1 *vector[32];
};
struct NolanBehaviour__Fields {
    struct EntityEventListener_1_IPlayerState___Fields _;
    struct Light *flashlightSpot;
    struct FlashlightDelayRotate *flashlightDelayRotate;
    struct Transform *flashlight;
    struct MeshRenderer *flashlightRenderer;
    struct Color flashlightUVColor;
    struct GameObject *flashlightBeam;
    struct Color flashlightBeamColor;
    struct Color flashlightBeamUVColor;
    struct GameObject *flashlightGlow;
    struct MeshRenderer *flashlightGlowRenderer;
    struct Material *flashlightGlowMaterial;
    struct Material *flashlightGlowUVMaterial;
    struct ParticleSystem__Array *_flashlightParticles_k__BackingField;
    struct AudioSource *flashlightAudioSource;
    struct AudioSource *flashlightFocusAudioSource;
    struct PlayRandomAudioClip *flashlightFocusRandomClips;
    struct PlayRandomAudioClip *flashlightFocusEndRandomClips;
    struct Vector3 flashlightBeamWindDirectionIndoors;
    struct Vector3 flashlightBeamWindDirectionOutdoors;
    float flashlightBeamRangeDefault;
    float flashlightBeamRangeIndoors;
    float flashlightBeamRangeOutdoors;
    float flashlightBeamIntensityDefault;
    float flashlightBeamIntensityHd;
    struct Vector3 fryFlashlightPosition;
    struct Vector3 fryFlashlightRotation;
    float fryFlashlightSpotAngle;
    float fryFlashlightIntensity;
    float fryFlashlightRange;
    float fryBeamAngle;
    float fryBeamIntensityInside;
    struct FlickerLightController *flashlightFlickerController;
    struct Light *volumetricLightBeam;
    bool disableFlashlightToggle;
    struct Transform *initialFlashlightTransform;
    struct Vector3 initialFlashlightRotation;
    float initialFlashlightRotationSpeed;
    struct VolumetricLight *volumetricLightComponent;
    float flashlightBeamIntensityCurrent;
    bool wasFlashlightOnBeforeHiding;
    bool wasFlashlightOnBeforeEmote;
    struct Color flashlightColor;
    float initialFlashlightSpotAngle;
    float initialFlashlightIntensity;
    float initialFlashlightRange;
    float initialBeamAngle;
    struct GameObject *carryObjectParent;
    float delayCarryObject;
    struct GameObject *carryObjectGoat;
    struct GameObject *carryObjectFirstAid;
    struct GameObject *carryObjectBleach;
    struct GameObject *carryObjectHay;
    struct GameObject *carryObjectGasoline;
    struct GameObject *carryObjectFuse;
    struct GameObject *carryObjectRat;
    struct GameObject *carryObjectRottenFood;
    struct GameObject *carryObjectBattery;
    struct GameObject *carryObjectEgg;
    struct GameObject *carryObjectMatchbox;
    struct GameObject *carryObjectRitualBook;
    struct GameObject *carryObjectPig;
    struct GameObject *carryObjectBone;
    struct GameObject *carryObjectCake;
    struct GameObject *carryObjectHead;
    struct GameObject *carryObjectSpade;
    struct AudioSource *carryObjectRitualBookAudio;
    struct InnEggRenderer *carryObjectEggRenderer;
    struct TownMatchboxRenderer *carryObjectMatchboxRenderer;
    struct TownRitualBookRenderer *carryObjectRitualRenderer;
    struct ManorHeadRenderer *carryObjectHeadRenderer;
    struct ParticleSystem *carryObjectBleachParticleSystem;
    struct ParticleSystem *carryObjectEggParticleSystem;
    struct Vector3 firstPersonLocalEggRotation;
    struct String *pendingCarryObjectName;
    struct String *pendingCarryObjectNameHost;
    bool pendingPlopObject;
    struct Vector3 placeRitualBookPosition;
    struct Vector3 placeRitualBookRotation;
    struct Vector3 initialRitualBookPosition;
    struct Quaternion initialRitualBookRotation;
    struct Transform *leftHandObjects;
    struct PlaceObjectPivot *placeObjectPivot;
    struct Transform *spadeLeftHandIKTarget;
    struct AudioSource *leftFootAudioSource;
    struct AudioSource *rightFootAudioSource;
    float footstepBufferTime;
    struct AudioSource *mouthAudioSource;
    float crouchVolumeLevel;
    float initialLeftFootAudioVolume;
    float initialRightFootAudioVolume;
    struct AudioSource *m_heartbeatAudioSource;
    struct AudioLowPassFilter *m_mainAudioListenerLowPassFilter;
    struct AudioSource *mazeSpawnAudioSource;
    struct PlayRandomAudioClip *bulletHitAudioClips;
    struct PlayRandomAudioClip *bulletMissWoodAudioClips;
    struct Vector3 rangedAttackMissAudioOffset;
    struct PlayRandomAudioClip *boarChargeImpactAudioClips;
    struct GameObject *m_Rain;
    struct ParticleSystem *m_RainParticleSystem;
    struct GameObject *m_SandStorm;
    struct ParticleSystem__Array *m_SandStormParticleSystems;
    struct GameObject *m_Splashes;
    struct GameObject *m_Snow;
    struct ParticleSystem__Array *m_SnowParticleSystems;
    struct GameObject *TVEInteractionElement;
    struct GameObject *cameraAnchor;
    struct Material *invisibleMat;
    struct Outline *outline;
    struct Transform *knockoutNeckIKTarget;
    struct HidingSpotController *pendingHidingSpot;
    struct DevourInput *devourInput;
    struct Animator *animator;
    struct AnimatorMonitor *animatorMonitor;
    struct UltimateCharacterLocomotion *locomotion;
    struct UltimateCharacterLocomotionHandler *locomotionHandler;
    struct CharacterIK *characterIK;
    struct Respawner *respawner;
    struct SpeedChange *speedChangeAbility;
    struct HeightChange *heightChangeAbility;
    struct Fry *fryAbility;
    struct Crawl *crawlAbility;
    struct Hiding *hidingAbility;
    struct DropObject *dropObjectAbility;
    struct Emote *emoteAbility;
    struct PlaceObject *placeObjectAbility;
    struct CharacterLoader *characterLoader;
    struct CustomPostProcessing *customPostProcessing;
    struct ColorGradingController *colorGradingController;
    struct GameObject *gameController;
    struct GameUI *gameUI;
    struct CameraController *cameraController;
    struct AttributeManager *attributeManager;
    bool annaIntroWasFlashlightOn;
    bool annaEnraged;
    struct CapsuleCollider *m_CapsuleCollider;
    struct CharacterLayerManager *m_CharacterLayerManager;
    struct NolanVoiceOvers *m_NolanVoiceOvers;
    bool flashlightFlickerActive;
    struct Coroutine *townIntroFlashlightFlickerCo;
    bool seenFocusFlashlightTutorial;
    float focusFlashlightTutorialTotalTimeToLookAtDemon;
    bool seenDropTutorial;
    bool flashlightTurnedOnOnce;
    bool seenShowKeysTutorial;
    bool seenBatteryTutorial;
    bool seenInnWebTutorial;
    float focusFlashlightTutorialTotalTimeToLookAtInnWeb;
    bool isBeingKnockedOut;
    bool isBeingCarried;
    bool inputDisabledFromKnockout;
    struct Coroutine *annaFlashlightFlickerCo;
    int32_t obstaclesLayerMask;
    int32_t characterLayerMask;
    int32_t stuckFrameCount;
    int32_t maxStuckFrameCount;
    bool characterCollisionsDisabled;
    struct Collider__Array *overlapCapsuleResults;
    struct List_1_NolanBehaviour_OverlappedCollider_ *overlappedColliders;
    struct Vector3__Array *m_raycastPoints;
    struct Vector3__Array *m_focusRaycastPoints;
    struct Single__Array *m_raycastRadii;
    struct Single__Array *m_focusRaycastRadii;
    struct Int32__Array *m_raycastNumPoints;
    struct Int32__Array *m_focusRaycastNumPoints;
    int32_t m_raycastLayerMask;
    struct RaycastHit m_lastHit;
    float m_raycastDistance;
    float m_focusRaycastDistance;
    int32_t m_raycastEdgeFrames;
    int32_t m_originalRaycastEdgeFrames;
    struct RaycastHit__Array *m_raycastResults;
    struct RaycastHit__Array *m_raycastAIHits;
    struct List_1_UnityEngine_RaycastHit_ *m_raycastValidAIHits;
    struct List_1_UnityEngine_Transform_ *m_lastHits;
    int32_t m_raycastFrequencyFramesPassed;
    struct IComparer_1_UnityEngine_RaycastHit_ *m_raycastDistanceComparer;
    struct Vector2 m_OriginalShakeForce;
    struct Survival *m_Survival;
    struct SurvivalVR *m_SurvivalVR;
    struct ToggleLocalRainOnPlayer__Array *m_ToggleLocalRainOnPlayer;
    struct List_1_System_String_ *m_raycastAITags;
    float m_lastStuckDetectionTime;
    struct Coroutine *m_zoomInCoroutine;
    struct Coroutine *m_zoomOutCoroutine;
    struct BeingHeld *m_beingHeldAbility;
    struct Coroutine *beingHeldCoroutine;
    int32_t m_numDemonsHoldingPlayer;
    struct List_1_UnityEngine_GameObject_ *m_demonsHoldingPlayer;
    struct Transform *m_demonHoldLeftFoot;
    struct Transform *m_demonHoldRightFoot;
    struct SurvivalObjectBurnController *burnController;
    struct MapController *mapController;
    struct SurvivalPlopLongInteractable *survivalPlopLongInteractable;
    struct ManorFountainInteractable *manorFountainInteractable;
    struct Coroutine *m_heartbeatCoroutine;
    int32_t characterLayer;
    int32_t waterLayer;
    int32_t azazelLayer;
    int32_t crawlLayer;
    #if defined(_CPLUSPLUS_)
    DevourGameMode__Enum m_gameMode;
    #else
    int32_t m_gameMode;
    #endif
    bool m_playerToPlayerCollisions;
    struct BoltEntity *_m_beingRevivedBy_k__BackingField;
    float m_beingRevivedProgress;
    bool m_isBeingRevivedByTeamLeader;
    bool m_isBeingRevivedByFieldMedic;
    bool m_isBeingRevivedByUnderPressure;
    struct Light *localLight;
    float localLightMazeIntensity;
    float localLightHidingIntensity;
    struct Attribute_1 *poisonAttribute;
    struct State *purgatoryState;
    struct State *uvTriggerState;
    struct Attribute_1 *batteryAttribute;
    bool batteryChargedBeforePurgatoryInNightmareMode;
    bool isUsingBatteryBonus;
    struct NolanPerkController *nolanPerkController;
    struct BoltEntity *lastToInflictPoison;
    bool isTeamLeaderPerkActive;
    bool isFieldMedicPerkActive;
    bool isFastWorkerPerkActive;
    bool isUnderPressurePerkActive;
    bool jumpScareActive;
    bool isHeadBobDisabled;
    struct State *crouchVentHeadBobDisabledState;
    struct DynamicBone__Array *dynamicBones;
    struct DynamicBoneCollider__Array *dynamicBoneColliders;
    struct PetLoader *petLoader;
    bool ventStateActive;
    bool azazelRunningAwayToCalmDown;
    bool otherPlayerPickedUpByAzazel;
    bool isUsingSpade;
    struct ManorShrineSoilInteractable *manorShrineSoilInteractable;
    bool isOnTopOfObjects;
    float isOnTopOfObjectsKillDistance;
    bool emoteStateActive;
    struct CharacterFootEffects *characterFootEffects;
    float lastPlayedFootstepTime;
    struct NolanClothSounds *nolanClothSounds;
    struct ManorDeadRealmTrigger *manorDeadRealmTrigger;
    float vrSnapTurnDegrees;
    float vrTurningSpeed;
    float vrTurningSensitivity;
    struct Vector3 vrCastOffset;
    bool vrRotating;
    struct RotateWithHead *rotateWithHeadAbility;
    struct List_1_Opsive_UltimateCharacterController_Character_Abilities_Interact_ *vrInteractAbilities;
    #if defined(_CPLUSPLUS_)
    VRMovementDirection__Enum vrMovementDirection;
    #else
    int32_t vrMovementDirection;
    #endif
    struct MoveWithVRCamera *moveWithVRCameraAbility;
    #if defined(_CPLUSPLUS_)
    VRTurningMode__Enum vrTurningMode;
    #else
    int32_t vrTurningMode;
    #endif
    bool vrIsHeightChangeActive;
    struct RaycastHit__Array *vrSmoothTurnRaycastResults;
    int32_t vrSmoothTurnRaycastLayerMask;
    struct RotateWithController *rotateWithControllerAbility;
    bool roomProtocolTokenIsNull;
};
struct NolanBehaviour {
    struct NolanBehaviour__Class *klass;
    MonitorData *monitor;
    struct NolanBehaviour__Fields fields;
};
struct MapController__Fields {
    struct EntityEventListener_1_IMapState___Fields _;
    struct AudioSource *onProgressAudioSource;
    float onProgressAudioDelay;
};
struct MapController {
    struct MapController__Class *klass;
    MonitorData *monitor;
    struct MapController__Fields fields;
};
struct InnMapController__Fields {
    struct MapController__Fields _;
    struct Survival *m_Survival;
    struct GameUI *gameUI;
    int32_t instancesPerMaze;
    struct GameObject__Array *mazePrefabs;
    struct Transform *mazeSpawnPointsParent;
    struct List_1_InnMazeController_ *mazes;
    struct List_1_InnHoleController_ *holes;
    struct List_1_System_Int32_ *eggTypes;
    struct List_1_System_Int32_ *eggsInWorld;
    struct AudioSource *introBGMAudioSource;
    struct PostProcessVolume *onsenCrawlingVolume;
    struct Material *nightmareZaraBodyMat;
    struct Material *innHoleFakeInteriorMaterial;
    struct GameObject *shrinePrefab;
    struct List_1_InnSymbol_ *symbols;
    struct GameObject__Array *initialShrineSpawnPoints;
    struct GameObject__Array *shrineSpawnPoints;
    struct List_1_InnShrineController_ *shrines;
    struct List_1_System_String_ *keysToSpawn;
    struct List_1_InitialGoatSpawn_ *initialShrinesUsed;
    struct Material *innHoleFakeInteriorMaterialInstance;
};
struct InnMapController {
    struct InnMapController__Class *klass;
    MonitorData *monitor;
    struct InnMapController__Fields fields;
};
struct NolanBehaviour__Array {
    struct NolanBehaviour__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct NolanBehaviour *vector[32];
};
struct LockedInteractable__Fields {
    struct MonoBehaviour__Fields _;
    bool isLocked;
    struct String *keyName;
    bool lockedOnAwake;
    struct DoorBehaviour *doorBehaviour;
    struct LockboxBehaviour *lockboxBehaviour;
};
struct LockedInteractable {
    struct LockedInteractable__Class *klass;
    MonitorData *monitor;
    struct LockedInteractable__Fields fields;
};
struct SurvivalObjectBurnController__Fields {
    struct EntityEventListener_1_IBurnTrapState___Fields _;
    struct Transform *m_GoatObject;
    struct KnockoutBox *knockoutBox;
    struct PoisonBox *poisonBox;
    struct AudioSource *m_BurnAudioSource;
    struct AudioSource *m_FireAudioSource;
    struct ParticleSystem *wildFireParticleSystem;
    struct ParticleSystem *smokeParticleSystem;
    struct ParticleSystem *redSmokeParticleSystem;
    struct Light *wildFireLight;
    float wildFireIntensity;
    struct AudioClip__Array *gasolineAudioClips;
    struct AudioClip__Array *goatPlopAudioClips;
    struct AudioClip__Array *goatBurnAudioClips;
    struct GameObject__Array *skullLitEffects;
    float gasolinePlopVolume;
    float goatPlopVolume;
    float goatPlopDelay;
    float goatBurnVolume;
    float gasolinePlopPitch;
    float goatPlopPitch;
    float goatBurnPitch;
    float goatBurnDelay;
    bool goatBurnOnActive;
    struct String *statNumObjectsUsed;
    struct Coroutine *lightFade;
    struct Animator *animator;
    bool IsBurningGoat;
    float wildFireOriginalVolume;
    struct Coroutine *wildFireVolumeCoroutine;
    struct GameUI *gameUI;
    struct GameObject *vrHandTriggerExtension;
};
struct SurvivalObjectBurnController {
    struct SurvivalObjectBurnController__Class *klass;
    MonitorData *monitor;
    struct SurvivalObjectBurnController__Fields fields;
};
struct Light__Array {
    struct Light__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Light *vector[32];
};
struct DevourInput__Fields {
    struct RewiredInput__Fields _;
    bool m_UseLongPressProgress;
    float m_LongPressProgressMultiplier;
    struct Dictionary_2_System_String_System_Single_ *m_ButtonDownProgress;
    struct State *m_TeamLeaderPerkState;
    struct State *m_UnderPressurePerkState;
    struct NolanBehaviour *m_NolanBehaviour;
    struct GameObject *m_GameObject;
    struct Player *m_Player;
    float m_CurrentFrame;
};
struct DevourInput {
    struct DevourInput__Class *klass;
    MonitorData *monitor;
    struct DevourInput__Fields fields;
};
struct CharacterEmote {
    struct CharacterEmote__Class *klass;
    MonitorData *monitor;
    struct CharacterEmote__Fields fields;
};
struct CharacterEmote__Array {
    struct CharacterEmote__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct CharacterEmote *vector[32];
};
struct SlaughterhouseAltarController__Fields {
    struct SurvivalObjectBurnController__Fields _;
    struct SlaughterhouseIntro *intro;
    struct EnragedAudioController *enragedAudioController;
    struct Survival *m_Survival;
    struct ColorGradingController *colorGradingController;
    struct SlaughterhousePurgatory__Array *purgatories;
    struct TeleportBox *purgatoryTeleportBox;
    struct Int32__Array *purgatoryCorpsesToSpawn;
    struct GameObject *purgatoryCorpsePrefab;
    struct Transform__Array *purgatoryRespawnPoints;
    struct ColorGradingController *purgatoryColorGradingController;
    struct SkinnedMeshRenderer__Array *pigSkinnedMeshRenderers;
    struct Animator *pigFixedJointAnimator;
    struct AudioSource *pigAudioSource;
    struct AudioClip__Array *pigPlaceAudioClips;
    struct GameObject *doorNumberPrefab;
    struct Animator *dunkerAnimator;
    struct GameObject *dunkerEffects;
    struct AudioSource *dunkerPowerOnAudio;
    struct AudioSource *dunkerPowerLoopAudio;
    struct PlayRandomAudioClip *dunkerPigDeathAudio;
    struct AudioSource *dunkerPowerOffAudio;
    struct AudioSource *dunkerGrindAudio;
    struct ParticleSystem__Array *dunkerParticleSystems;
    struct ThunderHouseShake *dunkerPowerOnShake;
    struct Transform *dunkerPentagramBlood;
    float dunkerPentagramBloodEndYPos;
    struct MeshRenderer *dunkerPentagramBloodRenderer;
    struct Transform *crankTransform;
    struct Transform *crankIKTarget;
    struct Outline *crankOutline;
    struct AudioSource *crankAudioSource;
    struct AudioSource *crankImpactAudioSource;
    struct AudioClip *turnCrankClip;
    struct AudioClip *endTurnCrankClip;
    struct AudioClip *resetCrankClip;
    struct AudioClip *endResetCrankClip;
    struct SlaughterhouseCrankInteractable *crankInteractable;
    struct Animator *hookAnimator;
    struct DynamicBone *hookDynamicBone;
    float hookSwingForceToZ;
    float hookSwingForceTime;
    struct Transform *hookTransform;
    float hookMaxY;
    struct AudioSource *chainAudioSource;
    struct AudioSource *chainImpactAudioSource;
    struct AudioClip *chainDownClip;
    struct AudioClip *chainUpClip;
    struct AudioClip *chainUpImpactClip;
    struct Transform *cageGoatSpawnPoint;
    struct Vector3 hookInitialPosition;
    struct Vector3 hookPosition;
    int32_t hookLeanTweenID;
    struct Coroutine *crankCo;
    float prevFloatMetadata;
    bool leverDownDone;
    bool spawnedFinalPigs;
    struct List_1_UnityEngine_GameObject_ *initialPigs;
    bool crankEndResetPlayed;
    struct Dictionary_2_UnityEngine_ParticleSystem_System_Single_ *originalRateOverTimes;
    float originalPigGibsBurstCount;
    float originalDunkerPentagramBloodYPos;
    struct Material *dunkerPentagramBloodMatInstance;
    struct Coroutine *dunkerPowerLoopCo;
    struct BoltEntity *_playerCranking_k__BackingField;
    int32_t purgatoryIndex;
    int32_t mazeIndex;
    int32_t numPurgatoryCorpsesToDestroy;
    struct Vector3 crankMaxRotation;
    struct List_1_AnimalGateSpawnPoint_ *animalGateSpawnPoints;
};
struct SlaughterhouseAltarController {
    struct SlaughterhouseAltarController__Class *klass;
    MonitorData *monitor;
    struct SlaughterhouseAltarController__Fields fields;
};
struct Transform__Array__Array {
    struct Transform__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Transform__Array *vector[32];
};
struct SurvivalAzazelBehaviour__Fields {
    struct EntityEventListener_1_IEnemyState___Fields _;
    struct Knockout *knockoutAbility;
    struct DropPlayer *dropPlayerAbility;
    struct SpeedChange *speedChangeAbility;
    struct Generic *genericEndingAbility;
    struct StopMovementAnimation *stopMovementAnimationAbility;
    struct AStarAIAgentMovement *aStarAbility;
    struct Animator *animator;
    struct AnimatorMonitor *animatorMonitor;
    struct UltimateCharacterLocomotion *locomotion;
    struct CharacterIK *characterIK;
    float initialIKHeadWeight;
    float initialIKBodyWeight;
    float initialIKHandWeight;
    struct Behavior__Array *behaviors;
    struct Behavior *mainBehavior;
    struct Behavior *initBehavior;
    struct Behavior *endingBehavior;
    struct RichAI *richAI;
    bool m_IsEnraged;
    struct AnnaDoorOpen *annaDoorOpenAbility;
    struct AnnaOpeningDoor *annaOpeningDoorAbility;
    struct Vector3 lastDestination;
    struct SurvivalObjectBurnController *burnController;
    struct MapController *mapController;
    #if defined(_CPLUSPLUS_)
    DevourGameMode__Enum m_gameMode;
    #else
    int32_t m_gameMode;
    #endif
    struct RagdollLoader *ragdollLoader;
    struct Survival *m_Survival;
    struct SurvivalVR *m_SurvivalVR;
    struct CharacterFootEffects *characterFootEffects;
    struct AIDestinationSetter *aiDestinationSetter;
    struct AudioSource *mainAudioSource;
    struct AudioSource *fryAudioSource;
    struct AudioSource *leftFootAudioSource;
    struct AudioSource *rightFootAudioSource;
    struct PlayRandomAudioClip *wanderClipPlayer;
    struct PlayRandomAudioClip *enragedClipPlayer;
    struct PlayRandomAudioClip *knockoutClipPlayer;
    struct PlayRandomAudioClip *screamClipPlayer;
    struct PlayRandomAudioClip *startWatchingClipPlayer;
    struct PlayRandomAudioClip *dropPlayerClipPlayer;
    struct PlayRandomAudioClip *staggerClipPlayer;
    struct PlayRandomAudioClip *calmClipPlayer;
    struct PlayRandomAudioClip *calm2DClipPlayer;
    struct PlayRandomAudioClip *calm2DVocalClipPlayer;
    struct PlayRandomAudioClip *possessedClipPlayer;
    struct PlayRandomAudioClip *painClipPlayer;
    struct PlayRandomAudioClip *clothMovementClipPlayer;
    struct AudioSource *knockoutClipBGMAudioSource;
    struct AudioSource *knockoutRangedClipBGMAudioSource;
    float wanderAudioMaxDistance;
    float wanderAudioVolume;
    float wanderAudioDelayMin;
    float wanderAudioDelayMax;
    float enrageFootMaxDistance;
    float enrageFootVolume;
    float calmIKHeadWeight;
    float focusIKBodyWeight;
    float defaultAudioMaxDistance;
    float defaultAudioVolume;
    float wanderAudioNextClipStart;
    float initialLeftFootAudioDistance;
    float initialRightFootAudioDistance;
    float initialLeftFootVolume;
    float initialRightFootVolume;
    struct AudioClip__Array *wanderBreathingClips1;
    struct AudioClip__Array *wanderBreathingClips2;
    struct AudioClip__Array *wanderBreathingClips3;
    struct AudioClip__Array *enragedBreathingClips1;
    struct AudioClip__Array *enragedBreathingClips2;
    struct AudioClip__Array *enragedBreathingClips3;
    struct SkinnedMeshRenderer__Array *eyesRenderers;
    struct SkinnedMeshRenderer__Array *bodyRenderers;
    struct Color enragedEyeColor;
    struct Color enragedEyeEmissionColor;
    struct Material *normalEyeMaterial;
    struct Material *enragedEyeMaterial;
    struct ParticleSystem *embersParticleSystem;
    float initialEmbersRateOverTime;
    struct ParticleSystem *smokeParticleSystem;
    float initialSmokeRateOverTime;
    struct AudioOcclusion *mainAudioSourceOcclusion;
    #if defined(_CPLUSPLUS_)
    Survival_AI__Enum m_AI;
    #else
    int32_t m_AI;
    #endif
    struct GameObject__Array *horns;
    struct WindZone *windZone;
    struct GameObject *enrageEffect;
    struct Coroutine *enragedEyesCoroutine;
    struct Transform *m_RightHand;
    struct Transform *m_PlayerPickedUpAnchor;
    struct KnockoutRagdoll *knockoutRagdoll;
    struct Vector3 knockoutRagdollOriginalPos;
    struct Quaternion knockoutRagdollOriginalRot;
    struct GameObject *ragdollTeleportPrefab;
    struct Transform *m_PlayerDropPoint;
    struct Coroutine *m_enrageEffectCo;
    struct Coroutine *m_delayMainAudioSourceCo;
    struct Transform *m_HeadTopEnd;
    struct NightmareMaterialLoader *nightmareMaterialLoader;
    struct String *enrageState;
    float windStrengthMoving;
    float m_KnockoutForceMagnitude;
    float m_KnockoutForceUpMultiplier;
    float m_possessedMotorAccelerationThroughDoors;
    bool debugStuckMarkSpot;
    struct IEnumerator *delayBreathingCoroutine;
    struct IEnumerator *startWatchingClipCoroutine;
    struct IEnumerator *delayBreathingVolumeCoroutine;
    struct AudioListener *m_MainAudioListener;
    struct Seeker *seeker;
    bool m_InsideAnnaAntiWanderCollider;
    struct GameUI *m_GameUI;
    float m_originalMotorAcceleration;
    struct CharacterLayerManager *m_CharacterLayerManager;
    struct LocalLookSource *m_LocalLookSource;
    struct Coroutine *delayedGoatScream;
    float annaStaggerCooldown;
    bool hasEnragedEyes;
    int32_t m_KnockoutCount;
    struct GameObject *m_PursueTarget;
    struct ScheduledEventBase *delayPickedUpPostProcessingEvent;
    struct ScheduledEventBase *knockoutScreamEvent;
    struct ScheduledEventBase *playDropPlayerClipEvent;
    struct ScheduledEventBase *spawnOnRagdollEvent;
    struct ScheduledEventBase *switchRenderersBackEvent;
    struct GameObject *m_heightFog;
    struct Vector3 m_heightFogOriginalScale;
    struct EnragedAudioController *enragedAudioController;
    int32_t obstaclesLayerMask;
    struct Collider__Array *overlapCapsuleResults;
    struct CapsuleCollider *m_CapsuleCollider;
    bool m_indoors;
    bool m_RecentlyAttached;
    bool vrMode;
};
struct SurvivalAzazelBehaviour {
    struct SurvivalAzazelBehaviour__Class *klass;
    MonitorData *monitor;
    struct SurvivalAzazelBehaviour__Fields fields;
};
struct Transform___VTable {
};
struct Transform___StaticFields {
};
struct Transform___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Transform___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Transform___VTable vtable;
};
struct GameConfigToken__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Read;
    VirtualInvokeData Write;
};
struct GameConfigToken__StaticFields {
};
struct GameConfigToken__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameConfigToken__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameConfigToken__VTable vtable;
};
struct Menu__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_list;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_list;
    VirtualInvokeData PersistBetweenStartupAndShutdown;
    VirtualInvokeData BoltShutdownBegin;
    VirtualInvokeData BoltStartBegin;
    VirtualInvokeData BoltStartDone;
    VirtualInvokeData BoltStartFailed;
    VirtualInvokeData StreamDataStarted;
    VirtualInvokeData StreamDataAborted;
    VirtualInvokeData StreamDataProgress;
    VirtualInvokeData StreamDataReceived;
    VirtualInvokeData SceneLoadLocalBegin;
    VirtualInvokeData SceneLoadLocalDone;
    VirtualInvokeData SceneLoadRemoteDone;
    VirtualInvokeData Connected;
    VirtualInvokeData ConnectFailed;
    VirtualInvokeData ConnectRequest;
    VirtualInvokeData ConnectRefused;
    VirtualInvokeData ConnectAttempt;
    VirtualInvokeData Disconnected;
    VirtualInvokeData ControlOfEntityLost;
    VirtualInvokeData ControlOfEntityGained;
    VirtualInvokeData EntityAttached;
    VirtualInvokeData EntityDetached;
    VirtualInvokeData EntityReceived;
    VirtualInvokeData EntityFrozen;
    VirtualInvokeData EntityThawed;
    VirtualInvokeData SessionListUpdated;
    VirtualInvokeData SessionConnected;
    VirtualInvokeData SessionConnectFailed;
    VirtualInvokeData SessionCreatedOrUpdated;
    VirtualInvokeData SessionCreationFailed;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnEvent_4;
    VirtualInvokeData OnEvent_5;
    VirtualInvokeData OnEvent_6;
    VirtualInvokeData OnEvent_7;
    VirtualInvokeData OnEvent_8;
    VirtualInvokeData OnEvent_9;
    VirtualInvokeData OnEvent_10;
    VirtualInvokeData OnEvent_11;
    VirtualInvokeData OnEvent_12;
    VirtualInvokeData OnEvent_13;
    VirtualInvokeData OnEvent_14;
    VirtualInvokeData OnEvent_15;
    VirtualInvokeData OnEvent_16;
    VirtualInvokeData OnEvent_17;
    VirtualInvokeData OnEvent_18;
    VirtualInvokeData OnEvent_19;
    VirtualInvokeData OnEvent_20;
    VirtualInvokeData OnEvent_21;
    VirtualInvokeData OnEvent_22;
    VirtualInvokeData OnEvent_23;
    VirtualInvokeData OnEvent_24;
    VirtualInvokeData OnEvent_25;
    VirtualInvokeData OnEvent_26;
    VirtualInvokeData OnEvent_27;
    VirtualInvokeData OnEvent_28;
    VirtualInvokeData OnEvent_29;
    VirtualInvokeData OnEvent_30;
    VirtualInvokeData OnEvent_31;
    VirtualInvokeData OnEvent_32;
    VirtualInvokeData OnEvent_33;
    VirtualInvokeData OnEvent_34;
    VirtualInvokeData OnEvent_35;
    VirtualInvokeData OnEvent_36;
    VirtualInvokeData OnEvent_37;
    VirtualInvokeData OnEvent_38;
    VirtualInvokeData OnEvent_39;
    VirtualInvokeData OnEvent_40;
    VirtualInvokeData OnEvent_41;
    VirtualInvokeData OnEvent_42;
    VirtualInvokeData OnEvent_43;
    VirtualInvokeData OnEvent_44;
    VirtualInvokeData OnEvent_45;
    VirtualInvokeData OnEvent_46;
    VirtualInvokeData OnEvent_47;
    VirtualInvokeData OnEvent_48;
    VirtualInvokeData OnEvent_49;
    VirtualInvokeData OnEvent_50;
    VirtualInvokeData OnEvent_51;
};
struct Menu__StaticFields {
    struct UdpChannelName SteamInventoryResultChannel;
    struct Dictionary_2_Horror_DevourMap_Horror_Menu_MapReleasedState_ *MapReleasedStates;
};
struct Menu__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Menu__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Menu__VTable vtable;
};
struct SurvivalLobbyController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
};
struct SurvivalLobbyController__StaticFields {
};
struct SurvivalLobbyController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SurvivalLobbyController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SurvivalLobbyController__VTable vtable;
};
struct Attribute_1__Array__VTable {
};
struct Attribute_1__Array__StaticFields {
};
struct Attribute_1__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Attribute_1__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Attribute_1__Array__VTable vtable;
};
struct MapController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnMapProgress;
};
struct MapController__StaticFields {
};
struct MapController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct MapController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct MapController__VTable vtable;
};
struct NolanBehaviour__Array__VTable {
};
struct NolanBehaviour__Array__StaticFields {
};
struct NolanBehaviour__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct NolanBehaviour__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct NolanBehaviour__Array__VTable vtable;
};
struct GameObject___VTable {
};
struct GameObject___StaticFields {
};
struct GameObject___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameObject___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameObject___VTable vtable;
};
struct LockedInteractable__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData CanInteract;
    VirtualInvokeData Interact;
};
struct LockedInteractable__StaticFields {
};
struct LockedInteractable__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct LockedInteractable__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct LockedInteractable__VTable vtable;
};
struct SurvivalObjectBurnController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData Awake;
    VirtualInvokeData OnActive;
    VirtualInvokeData OnGoat;
    VirtualInvokeData OnNumObjectsBurnt;
    VirtualInvokeData SkipToGoat;
    VirtualInvokeData DestroyGoat;
    VirtualInvokeData PlaceGoat;
    VirtualInvokeData CanKnockoutPlayers;
};
struct SurvivalObjectBurnController__StaticFields {
};
struct SurvivalObjectBurnController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SurvivalObjectBurnController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SurvivalObjectBurnController__VTable vtable;
};
struct Light__Array__VTable {
};
struct Light__Array__StaticFields {
};
struct Light__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Light__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Light__Array__VTable vtable;
};
struct InnMapController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnMapProgress;
};
struct InnMapController__StaticFields {
};
struct InnMapController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct InnMapController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct InnMapController__VTable vtable;
};
struct DevourInput__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData StateWillChange;
    VirtualInvokeData StateChange;
    VirtualInvokeData Awake;
    VirtualInvokeData StateWillChange_1;
    VirtualInvokeData StateChange_1;
    VirtualInvokeData GetButtonInternal;
    VirtualInvokeData GetButtonDownInternal;
    VirtualInvokeData GetButtonUpInternal;
    VirtualInvokeData GetDoublePressInternal;
    VirtualInvokeData GetLongPress;
    VirtualInvokeData GetAxisInternal;
    VirtualInvokeData GetAxisRawInternal;
    VirtualInvokeData GetMousePosition;
    VirtualInvokeData GetLookVector;
    VirtualInvokeData IsPointerOverUI;
    VirtualInvokeData EnableGameplayInput;
    VirtualInvokeData OnApplicationFocus;
};
struct DevourInput__StaticFields {
};
struct DevourInput__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct DevourInput__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct DevourInput__VTable vtable;
};
struct CharacterEmote__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct CharacterEmote__StaticFields {
};
struct CharacterEmote__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterEmote__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterEmote__VTable vtable;
};
struct CharacterEmote__Array__VTable {
};
struct CharacterEmote__Array__StaticFields {
};
struct CharacterEmote__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterEmote__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterEmote__Array__VTable vtable;
};
struct NolanBehaviour__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
};
struct NolanBehaviour__StaticFields {
};
struct NolanBehaviour__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct NolanBehaviour__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct NolanBehaviour__VTable vtable;
};
struct Transform__Array__Array__VTable {
};
struct Transform__Array__Array__StaticFields {
};
struct Transform__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Transform__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Transform__Array__Array__VTable vtable;
};
struct SlaughterhouseAltarController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData Awake;
    VirtualInvokeData OnActive;
    VirtualInvokeData OnGoat;
    VirtualInvokeData OnNumObjectsBurnt;
    VirtualInvokeData SkipToGoat;
    VirtualInvokeData DestroyGoat;
    VirtualInvokeData PlaceGoat;
    VirtualInvokeData CanKnockoutPlayers;
};
struct SlaughterhouseAltarController__StaticFields {
};
struct SlaughterhouseAltarController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SlaughterhouseAltarController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SlaughterhouseAltarController__VTable vtable;
};
struct SurvivalAzazelBehaviour__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnAddressableMaterialLoaderComplete;
    VirtualInvokeData PlayEnrageEffect;
    VirtualInvokeData EnragedEyes;
    VirtualInvokeData Spawn;
    VirtualInvokeData OnLookAtEntity;
    VirtualInvokeData PlayWanderClip;
    VirtualInvokeData CanFinalGoatBurnt;
    VirtualInvokeData CanDelayScream;
    VirtualInvokeData Landed;
    VirtualInvokeData StopPursueIfNecessary;
};
struct SurvivalAzazelBehaviour__StaticFields {
};
struct SurvivalAzazelBehaviour__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SurvivalAzazelBehaviour__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SurvivalAzazelBehaviour__VTable vtable;
};
struct Survival__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_list;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_list;
    VirtualInvokeData PersistBetweenStartupAndShutdown;
    VirtualInvokeData BoltShutdownBegin;
    VirtualInvokeData BoltStartBegin;
    VirtualInvokeData BoltStartDone;
    VirtualInvokeData BoltStartFailed;
    VirtualInvokeData StreamDataStarted;
    VirtualInvokeData StreamDataAborted;
    VirtualInvokeData StreamDataProgress;
    VirtualInvokeData StreamDataReceived;
    VirtualInvokeData SceneLoadLocalBegin;
    VirtualInvokeData SceneLoadLocalDone;
    VirtualInvokeData SceneLoadRemoteDone;
    VirtualInvokeData Connected;
    VirtualInvokeData ConnectFailed;
    VirtualInvokeData ConnectRequest;
    VirtualInvokeData ConnectRefused;
    VirtualInvokeData ConnectAttempt;
    VirtualInvokeData Disconnected;
    VirtualInvokeData ControlOfEntityLost;
    VirtualInvokeData ControlOfEntityGained;
    VirtualInvokeData EntityAttached;
    VirtualInvokeData EntityDetached;
    VirtualInvokeData EntityReceived;
    VirtualInvokeData EntityFrozen;
    VirtualInvokeData EntityThawed;
    VirtualInvokeData SessionListUpdated;
    VirtualInvokeData SessionConnected;
    VirtualInvokeData SessionConnectFailed;
    VirtualInvokeData SessionCreatedOrUpdated;
    VirtualInvokeData SessionCreationFailed;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnEvent_4;
    VirtualInvokeData OnEvent_5;
    VirtualInvokeData OnEvent_6;
    VirtualInvokeData OnEvent_7;
    VirtualInvokeData OnEvent_8;
    VirtualInvokeData OnEvent_9;
    VirtualInvokeData OnEvent_10;
    VirtualInvokeData OnEvent_11;
    VirtualInvokeData OnEvent_12;
    VirtualInvokeData OnEvent_13;
    VirtualInvokeData OnEvent_14;
    VirtualInvokeData OnEvent_15;
    VirtualInvokeData OnEvent_16;
    VirtualInvokeData OnEvent_17;
    VirtualInvokeData OnEvent_18;
    VirtualInvokeData OnEvent_19;
    VirtualInvokeData OnEvent_20;
    VirtualInvokeData OnEvent_21;
    VirtualInvokeData OnEvent_22;
    VirtualInvokeData OnEvent_23;
    VirtualInvokeData OnEvent_24;
    VirtualInvokeData OnEvent_25;
    VirtualInvokeData OnEvent_26;
    VirtualInvokeData OnEvent_27;
    VirtualInvokeData OnEvent_28;
    VirtualInvokeData OnEvent_29;
    VirtualInvokeData OnEvent_30;
    VirtualInvokeData OnEvent_31;
    VirtualInvokeData OnEvent_32;
    VirtualInvokeData OnEvent_33;
    VirtualInvokeData OnEvent_34;
    VirtualInvokeData OnEvent_35;
    VirtualInvokeData OnEvent_36;
    VirtualInvokeData OnEvent_37;
    VirtualInvokeData OnEvent_38;
    VirtualInvokeData OnEvent_39;
    VirtualInvokeData OnEvent_40;
    VirtualInvokeData OnEvent_41;
    VirtualInvokeData OnEvent_42;
    VirtualInvokeData OnEvent_43;
    VirtualInvokeData OnEvent_44;
    VirtualInvokeData OnEvent_45;
    VirtualInvokeData OnEvent_46;
    VirtualInvokeData OnEvent_47;
    VirtualInvokeData OnEvent_48;
    VirtualInvokeData OnEvent_49;
    VirtualInvokeData OnEvent_50;
    VirtualInvokeData OnEvent_51;
};
struct Survival__StaticFields {
};
struct Survival__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Survival__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Survival__VTable vtable;
};
struct CharacterFlashlight {
    struct CharacterFlashlight__Class *klass;
    MonitorData *monitor;
    struct CharacterFlashlight__Fields fields;
};
struct CharacterFlashlight__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct CharacterFlashlight__StaticFields {
};
struct CharacterFlashlight__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterFlashlight__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterFlashlight__VTable vtable;
};
struct SurvivalInteractable__Fields {
    struct ObjectInteractable__Fields _;
    struct AudioClip *pickupAudioClip;
    struct AudioClip__Array *pickupAudioClips;
    struct AudioClip__Array *slaughterhouseGasolineAudioClips;
    struct AudioClip__Array *manorBleachAudioClips;
    float pickupAudioVolume;
    struct String *prefabName;
    bool _startingCarry_k__BackingField;
    struct IInteractObjectState *interactObjectState;
};
struct SurvivalInteractable {
    struct SurvivalInteractable__Class *klass;
    MonitorData *monitor;
    struct SurvivalInteractable__Fields fields;
};
struct SurvivalInteractable__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_prev;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_next;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__get_list;
    VirtualInvokeData Photon_Bolt_Collections_IBoltListNode_Photon_Bolt_Internal_GlobalEventListenerBase__set_list;
    VirtualInvokeData PersistBetweenStartupAndShutdown;
    VirtualInvokeData BoltShutdownBegin;
    VirtualInvokeData BoltStartBegin;
    VirtualInvokeData BoltStartDone;
    VirtualInvokeData BoltStartFailed;
    VirtualInvokeData StreamDataStarted;
    VirtualInvokeData StreamDataAborted;
    VirtualInvokeData StreamDataProgress;
    VirtualInvokeData StreamDataReceived;
    VirtualInvokeData SceneLoadLocalBegin;
    VirtualInvokeData SceneLoadLocalDone;
    VirtualInvokeData SceneLoadRemoteDone;
    VirtualInvokeData Connected;
    VirtualInvokeData ConnectFailed;
    VirtualInvokeData ConnectRequest;
    VirtualInvokeData ConnectRefused;
    VirtualInvokeData ConnectAttempt;
    VirtualInvokeData Disconnected;
    VirtualInvokeData ControlOfEntityLost;
    VirtualInvokeData ControlOfEntityGained;
    VirtualInvokeData EntityAttached;
    VirtualInvokeData EntityDetached;
    VirtualInvokeData EntityReceived;
    VirtualInvokeData EntityFrozen;
    VirtualInvokeData EntityThawed;
    VirtualInvokeData SessionListUpdated;
    VirtualInvokeData SessionConnected;
    VirtualInvokeData SessionConnectFailed;
    VirtualInvokeData SessionCreatedOrUpdated;
    VirtualInvokeData SessionCreationFailed;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
    VirtualInvokeData OnEvent_4;
    VirtualInvokeData OnEvent_5;
    VirtualInvokeData OnEvent_6;
    VirtualInvokeData OnEvent_7;
    VirtualInvokeData OnEvent_8;
    VirtualInvokeData OnEvent_9;
    VirtualInvokeData OnEvent_10;
    VirtualInvokeData OnEvent_11;
    VirtualInvokeData OnEvent_12;
    VirtualInvokeData OnEvent_13;
    VirtualInvokeData OnEvent_14;
    VirtualInvokeData OnEvent_15;
    VirtualInvokeData OnEvent_16;
    VirtualInvokeData OnEvent_17;
    VirtualInvokeData OnEvent_18;
    VirtualInvokeData OnEvent_19;
    VirtualInvokeData OnEvent_20;
    VirtualInvokeData OnEvent_21;
    VirtualInvokeData OnEvent_22;
    VirtualInvokeData OnEvent_23;
    VirtualInvokeData OnEvent_24;
    VirtualInvokeData OnEvent_25;
    VirtualInvokeData OnEvent_26;
    VirtualInvokeData OnEvent_27;
    VirtualInvokeData OnEvent_28;
    VirtualInvokeData OnEvent_29;
    VirtualInvokeData OnEvent_30;
    VirtualInvokeData OnEvent_31;
    VirtualInvokeData OnEvent_32;
    VirtualInvokeData OnEvent_33;
    VirtualInvokeData OnEvent_34;
    VirtualInvokeData OnEvent_35;
    VirtualInvokeData OnEvent_36;
    VirtualInvokeData OnEvent_37;
    VirtualInvokeData OnEvent_38;
    VirtualInvokeData OnEvent_39;
    VirtualInvokeData OnEvent_40;
    VirtualInvokeData OnEvent_41;
    VirtualInvokeData OnEvent_42;
    VirtualInvokeData OnEvent_43;
    VirtualInvokeData OnEvent_44;
    VirtualInvokeData OnEvent_45;
    VirtualInvokeData OnEvent_46;
    VirtualInvokeData OnEvent_47;
    VirtualInvokeData OnEvent_48;
    VirtualInvokeData OnEvent_49;
    VirtualInvokeData OnEvent_50;
    VirtualInvokeData OnEvent_51;
    VirtualInvokeData CanInteract;
    VirtualInvokeData Interact;
    VirtualInvokeData AbilityMessage;
    VirtualInvokeData Awake;
    VirtualInvokeData Start;
    VirtualInvokeData CanInteract_1;
    VirtualInvokeData Interact_1;
    VirtualInvokeData ShouldAllowInteractRequest;
    VirtualInvokeData OnInteractRequestAllowed;
    VirtualInvokeData GetPendingCarryObjectNameHost;
    VirtualInvokeData AbilityIcon;
};
struct SurvivalInteractable__StaticFields {
};
struct SurvivalInteractable__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SurvivalInteractable__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SurvivalInteractable__VTable vtable;
};
struct NolanRankController__Fields {
    struct EntityEventListener_1_IPlayerState___Fields _;
    struct SurvivalLobbyController *survivalLobbyController;
};
struct NolanRankController {
    struct NolanRankController__Class *klass;
    MonitorData *monitor;
    struct NolanRankController__Fields fields;
};
struct NolanRankController__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData get_invoke;
    VirtualInvokeData get_entity;
    VirtualInvokeData set_entity;
    VirtualInvokeData Initialized;
    VirtualInvokeData Attached;
    VirtualInvokeData Detached;
    VirtualInvokeData SimulateOwner;
    VirtualInvokeData SimulateController;
    VirtualInvokeData ControlLost;
    VirtualInvokeData ControlGained;
    VirtualInvokeData MissingCommand;
    VirtualInvokeData ExecuteCommand;
    VirtualInvokeData LocalAndRemoteResultEqual;
    VirtualInvokeData Initialized_1;
    VirtualInvokeData Attached_1;
    VirtualInvokeData Detached_1;
    VirtualInvokeData SimulateOwner_1;
    VirtualInvokeData SimulateController_1;
    VirtualInvokeData ControlGained_1;
    VirtualInvokeData ControlLost_1;
    VirtualInvokeData MissingCommand_1;
    VirtualInvokeData ExecuteCommand_1;
    VirtualInvokeData LocalAndRemoteResultEqual_1;
    VirtualInvokeData OnEvent;
    VirtualInvokeData OnEvent_1;
    VirtualInvokeData OnEvent_2;
    VirtualInvokeData OnEvent_3;
};
struct NolanRankController__StaticFields {
};
struct NolanRankController__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct NolanRankController__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct NolanRankController__VTable vtable;
};
struct SteamInventoryManager__Fields {
    struct MonoBehaviour__Fields _;
    struct SteamInventoryResult_t m_SteamInventoryResult;
    struct SteamItemDetails_t__Array *m_SteamItemDetails;
    struct Callback_1_SteamInventoryResultReady_t_ *m_SteamInventoryResultReady;
    struct Callback_1_SteamInventoryFullUpdate_t_ *m_SteamInventoryFullUpdate;
    struct Callback_1_SteamInventoryDefinitionUpdate_t_ *m_SteamInventoryDefinitionUpdate;
    struct CallResult_1_SteamInventoryStartPurchaseResult_t_ *m_SteamInventoryStartPurchaseResult;
    struct CallResult_1_SteamInventoryRequestPricesResult_t_ *m_SteamInventoryRequestPricesResult;
    struct List_1_SteamInventoryItem_ *steamItems;
    struct List_1_Steamworks_SteamItemDef_t_ *userItems;
    struct UnityAction *userInventoryAction;
    bool isExitingGame;
    bool retrievedUserItems;
    struct SteamInventoryResult_t m_ServerValidationResult;
    bool _resendClientInventory_k__BackingField;
    bool addPromoItemLock;
    struct Coroutine *addPromoItemTimeoutCo;
    bool _noItems_k__BackingField;
};
struct SteamInventoryManager {
    struct SteamInventoryManager__Class *klass;
    MonitorData *monitor;
    struct SteamInventoryManager__Fields fields;
};
struct SteamInventoryManager__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct SteamInventoryManager__StaticFields {
    struct SteamInventoryManager *instance;
};
struct SteamInventoryManager__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SteamInventoryManager__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SteamInventoryManager__VTable vtable;
};
struct CharacterOutfit {
    struct CharacterOutfit__Class *klass;
    MonitorData *monitor;
    struct CharacterOutfit__Fields fields;
};
struct CharacterOutfit__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct CharacterOutfit__StaticFields {
};
struct CharacterOutfit__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterOutfit__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterOutfit__VTable vtable;
};
struct CharacterPerk {
    struct CharacterPerk__Class *klass;
    MonitorData *monitor;
    struct CharacterPerk__Fields fields;
};
struct CharacterPerk__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct CharacterPerk__StaticFields {
};
struct CharacterPerk__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterPerk__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterPerk__VTable vtable;
};
struct UIEmoteSelectionType__Fields {
    struct MonoBehaviour__Fields _;
    struct CharacterEmote *emoteType;
    struct GameObject *selection;
    struct GameObject *emoteCost;
    struct Text *emoteCostText;
    struct Button *button;
    struct GameObject *owned;
};
struct UIEmoteSelectionType {
    struct UIEmoteSelectionType__Class *klass;
    MonitorData *monitor;
    struct UIEmoteSelectionType__Fields fields;
};
struct UIEmoteSelectionType__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData OnSelect;
};
struct UIEmoteSelectionType__StaticFields {
};
struct UIEmoteSelectionType__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIEmoteSelectionType__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIEmoteSelectionType__VTable vtable;
};
struct UIFlashlightSelectionType__Fields {
    struct MonoBehaviour__Fields _;
    struct CharacterFlashlight *flashlightType;
    struct GameObject *selection;
    struct GameObject *flashlightCost;
    struct Text *flashlightCostText;
    struct Button *button;
    struct GameObject *owned;
};
struct UIFlashlightSelectionType {
    struct UIFlashlightSelectionType__Class *klass;
    MonitorData *monitor;
    struct UIFlashlightSelectionType__Fields fields;
};
struct UIFlashlightSelectionType__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData OnSelect;
};
struct UIFlashlightSelectionType__StaticFields {
};
struct UIFlashlightSelectionType__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIFlashlightSelectionType__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIFlashlightSelectionType__VTable vtable;
};
struct UIOutfitSelectionType__Fields {
    struct MonoBehaviour__Fields _;
    #if defined(_CPLUSPLUS_)
    Robe__Enum robeType;
    #else
    int32_t robeType;
    #endif
    struct CharacterOutfit *outfitType;
    bool isRobe;
    struct GameObject *selection;
    struct Button *button;
    struct GameObject *owned;
    struct OutfitPreviewLoader *outfitPreviewLoader;
};
struct UIOutfitSelectionType {
    struct UIOutfitSelectionType__Class *klass;
    MonitorData *monitor;
    struct UIOutfitSelectionType__Fields fields;
};
struct UIOutfitSelectionType__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData OnSelect;
};
struct UIOutfitSelectionType__StaticFields {
};
struct UIOutfitSelectionType__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIOutfitSelectionType__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIOutfitSelectionType__VTable vtable;
};
struct UIPerkSelectionType__Fields {
    struct MonoBehaviour__Fields _;
    struct CharacterPerk *perkType;
    struct GameObject *selection;
    struct GameObject *perkCost;
    struct Text *perkCostText;
    struct Button *button;
    struct GameObject *owned;
    struct Image *disabledOverlay;
    struct Image *background;
};
struct UIPerkSelectionType {
    struct UIPerkSelectionType__Class *klass;
    MonitorData *monitor;
    struct UIPerkSelectionType__Fields fields;
};
struct UIPerkSelectionType__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct UIPerkSelectionType__StaticFields {
};
struct UIPerkSelectionType__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIPerkSelectionType__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIPerkSelectionType__VTable vtable;
};
struct SurvivalReviveInteractable__Fields {
    struct MonoBehaviour__Fields _;
    struct UltimateCharacterLocomotion *locomotion;
    struct BoltEntity *entity;
    struct Crawl *crawlAbility;
};
struct SurvivalReviveInteractable {
    struct SurvivalReviveInteractable__Class *klass;
    MonitorData *monitor;
    struct SurvivalReviveInteractable__Fields fields;
};
struct SurvivalReviveInteractable__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData CanInteract;
    VirtualInvokeData Interact;
    VirtualInvokeData AbilityMessage;
    VirtualInvokeData Awake;
};
struct SurvivalReviveInteractable__StaticFields {
};
struct SurvivalReviveInteractable__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct SurvivalReviveInteractable__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct SurvivalReviveInteractable__VTable vtable;
};
struct OptionsHelpers__Fields {
    struct MonoBehaviour__Fields _;
    struct Brightness *brightnessController;
    struct GameObject *altTabWarning;
    struct GameObject *resolutionModalItem;
    struct GameObject *keyBindingPrefab;
    struct GameObject *keyBindingCategoryHeaderPrefab;
    struct Sprite *keyBindingFilledFrame;
    struct Sprite *keyBindingEmptyFrame;
    struct AudioMixer *mixer;
    struct CharacterData__Array *characterData;
    struct CharacterRobe__Array *robes;
    struct List_1_CharacterOutfit_ *outfits;
    struct List_1_CharacterPerk_ *perks;
    struct List_1_CharacterFlashlight_ *flashlights;
    struct List_1_CharacterPet_ *pets;
    struct List_1_CharacterEmote_ *emotes;
    bool _isProfanityAllowed_k__BackingField;
    bool _muteJumpScares_k__BackingField;
    bool _gamepadRumble_k__BackingField;
    bool _htcViveTrackpadClickMode_k__BackingField;
    bool _vrHandTracking_k__BackingField;
    bool _inGame_k__BackingField;
    bool _tutorialsDisabled_k__BackingField;
    bool _bloodEffects_k__BackingField;
    bool _ventAutoCrouch_k__BackingField;
    bool _classicEmotes_k__BackingField;
    bool debugUnlockAll;
    bool isBetaMessageSeen;
    struct AudioMixerGroup *soundEffectsAudioMixerGroup;
    int32_t selectedResolutionIndex;
    int32_t selectedDisplayMode;
    int32_t activeDisplayMonitor;
    struct CameraController *cameraController;
    struct MotionBlurController *motionBlurController;
    struct List_1_UnityEngine_Resolution_ *resolutions;
    struct Button *buttonListening;
    bool listeningForBinding;
    struct InputAction *listeningAction;
    bool listeningForGamepadInput;
    struct Dictionary_2_System_String_System_Action_ *bindingsChangedListeners;
    struct GameUI *gameUI;
    struct Survival *m_Survival;
    struct SurvivalVR *m_SurvivalVR;
    struct VRFollowMainCamera *vrFollowMainCamera;
};
struct OptionsHelpers {
    struct OptionsHelpers__Class *klass;
    MonitorData *monitor;
    struct OptionsHelpers__Fields fields;
};
struct CharacterOutfit__Array {
    struct CharacterOutfit__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct CharacterOutfit *vector[32];
};
struct CharacterPerk__Array {
    struct CharacterPerk__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct CharacterPerk *vector[32];
};
struct CharacterFlashlight__Array {
    struct CharacterFlashlight__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct CharacterFlashlight *vector[32];
};
struct CharacterOutfit__Array__VTable {
};
struct CharacterOutfit__Array__StaticFields {
};
struct CharacterOutfit__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterOutfit__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterOutfit__Array__VTable vtable;
};
struct CharacterPerk__Array__VTable {
};
struct CharacterPerk__Array__StaticFields {
};
struct CharacterPerk__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterPerk__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterPerk__Array__VTable vtable;
};
struct CharacterFlashlight__Array__VTable {
};
struct CharacterFlashlight__Array__StaticFields {
};
struct CharacterFlashlight__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct CharacterFlashlight__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct CharacterFlashlight__Array__VTable vtable;
};
struct OptionsHelpers__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct OptionsHelpers__StaticFields {
    struct OptionsHelpers *instance;
    struct Int32__Array *VR_TURNING_ANGLES;
};
struct OptionsHelpers__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct OptionsHelpers__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct OptionsHelpers__VTable vtable;
};
struct RankHelpers__Fields {
    struct MonoBehaviour__Fields _;
    struct List_1_RankHelpers_RankSprite_ *rankSprites;
};
struct RankHelpers {
    struct RankHelpers__Class *klass;
    MonitorData *monitor;
    struct RankHelpers__Fields fields;
};
struct RankHelpers__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct RankHelpers__StaticFields {
    struct RankHelpers *instance;
    int32_t MaxRank;
};
struct RankHelpers__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct RankHelpers__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct RankHelpers__VTable vtable;
};
struct RankHelpers_ExpGainInfo {
    struct RankHelpers_ExpGainInfo__Class *klass;
    MonitorData *monitor;
    struct RankHelpers_ExpGainInfo__Fields fields;
};
struct RankHelpers_ExpGainInfo__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct RankHelpers_ExpGainInfo__StaticFields {
};
struct RankHelpers_ExpGainInfo__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct RankHelpers_ExpGainInfo__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct RankHelpers_ExpGainInfo__VTable vtable;
};
struct UIOutfitSelectionType__Array {
    struct UIOutfitSelectionType__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct UIOutfitSelectionType *vector[32];
};
struct UIOutfitSelectionType__Array__VTable {
};
struct UIOutfitSelectionType__Array__StaticFields {
};
struct UIOutfitSelectionType__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIOutfitSelectionType__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIOutfitSelectionType__Array__VTable vtable;
};
struct UIPerkSelectionType__Array {
    struct UIPerkSelectionType__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct UIPerkSelectionType *vector[32];
};
struct UIPerkSelectionType__Array__VTable {
};
struct UIPerkSelectionType__Array__StaticFields {
};
struct UIPerkSelectionType__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIPerkSelectionType__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIPerkSelectionType__Array__VTable vtable;
};
struct UIFlashlightSelectionType__Array {
    struct UIFlashlightSelectionType__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct UIFlashlightSelectionType *vector[32];
};
struct UIFlashlightSelectionType__Array__VTable {
};
struct UIFlashlightSelectionType__Array__StaticFields {
};
struct UIFlashlightSelectionType__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIFlashlightSelectionType__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIFlashlightSelectionType__Array__VTable vtable;
};
struct UIEmoteSelectionType__Array {
    struct UIEmoteSelectionType__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct UIEmoteSelectionType *vector[32];
};
struct UIEmoteSelectionType__Array__VTable {
};
struct UIEmoteSelectionType__Array__StaticFields {
};
struct UIEmoteSelectionType__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UIEmoteSelectionType__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UIEmoteSelectionType__Array__VTable vtable;
};
struct Component__Array {
    struct Component__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Component *vector[32];
};
struct Component__Array__VTable {
};
struct Component__Array__StaticFields {
};
struct Component__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Component__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Component__Array__VTable vtable;
};
struct Vector3__Array__Array {
    struct Vector3__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Vector3__Array *vector[32];
};
struct Vector3__Array__Array__VTable {
};
struct Vector3__Array__Array__StaticFields {
};
struct Vector3__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Vector3__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Vector3__Array__Array__VTable vtable;
};
struct UltimateCharacterLocomotion__Array {
    struct UltimateCharacterLocomotion__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct UltimateCharacterLocomotion *vector[32];
};
struct UltimateCharacterLocomotion__Array__VTable {
};
struct UltimateCharacterLocomotion__Array__StaticFields {
};
struct UltimateCharacterLocomotion__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct UltimateCharacterLocomotion__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct UltimateCharacterLocomotion__Array__VTable vtable;
};
struct String__Array__Array {
    struct String__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct String__Array *vector[32];
};
struct String__Array__Array__VTable {
};
struct String__Array__Array__StaticFields {
};
struct String__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct String__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct String__Array__Array__VTable vtable;
};
struct Camera___VTable {
};
struct Camera___StaticFields {
};
struct Camera___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Camera___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Camera___VTable vtable;
};
struct Vector3__1__VTable {
};
struct Vector3__1__StaticFields {
};
struct Vector3__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Vector3__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Vector3__1__VTable vtable;
};
struct Scale {
    struct Vector3 m_Scale;
    bool m_IsNone;
};
struct Scale__Boxed {
    struct Scale__Class *klass;
    MonitorData *monitor;
    struct Scale fields;
};
struct Scale__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
    VirtualInvokeData Equals_1;
};
struct Scale__StaticFields {
};
struct Scale__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Scale__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Scale__VTable vtable;
};
struct Scale__Array {
    struct Scale__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Scale vector[32];
};
struct Scale__Array__VTable {
};
struct Scale__Array__StaticFields {
};
struct Scale__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Scale__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Scale__Array__VTable vtable;
};
struct Scale___VTable {
};
struct Scale___StaticFields {
};
struct Scale___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Scale___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Scale___VTable vtable;
};
struct Object__2__VTable {
};
struct Object__2__StaticFields {
};
struct Object__2__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__2__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__2__VTable vtable;
};
struct Camera__Array {
    struct Camera__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Camera *vector[32];
};
struct Camera__Array__VTable {
};
struct Camera__Array__StaticFields {
};
struct Camera__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Camera__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Camera__Array__VTable vtable;
};
struct GameObject__Array__Array {
    struct GameObject__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct GameObject__Array *vector[32];
};
struct GameObject__Array__Array__VTable {
};
struct GameObject__Array__Array__StaticFields {
};
struct GameObject__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameObject__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameObject__Array__Array__VTable vtable;
};
struct GameObject__1__VTable {
};
struct GameObject__1__StaticFields {
};
struct GameObject__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct GameObject__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct GameObject__1__VTable vtable;
};
struct Component___VTable {
};
struct Component___StaticFields {
};
struct Component___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Component___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Component___VTable vtable;
};
struct Quaternion__Array__Array {
    struct Quaternion__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Quaternion__Array *vector[32];
};
struct Quaternion__Array__Array__VTable {
};
struct Quaternion__Array__Array__StaticFields {
};
struct Quaternion__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Quaternion__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Quaternion__Array__Array__VTable vtable;
};
struct Quaternion__1__VTable {
};
struct Quaternion__1__StaticFields {
};
struct Quaternion__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Quaternion__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Quaternion__1__VTable vtable;
};
struct PrefabId__Array {
    struct PrefabId__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct PrefabId vector[32];
};
struct PrefabId__Array__VTable {
};
struct PrefabId__Array__StaticFields {
};
struct PrefabId__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct PrefabId__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct PrefabId__Array__VTable vtable;
};
struct Transform__1__VTable {
};
struct Transform__1__StaticFields {
};
struct Transform__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Transform__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Transform__1__VTable vtable;
};
struct Object__3__VTable {
};
struct Object__3__StaticFields {
};
struct Object__3__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__3__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__3__VTable vtable;
};
struct Object__Array__Array {
    struct Object__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Object__Array *vector[32];
};
struct Object__Array__Array__VTable {
};
struct Object__Array__Array__StaticFields {
};
struct Object__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Object__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Object__Array__Array__VTable vtable;
};
struct PrefabId___VTable {
};
struct PrefabId___StaticFields {
};
struct PrefabId___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct PrefabId___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct PrefabId___VTable vtable;
};
struct Scale__1__VTable {
};
struct Scale__1__StaticFields {
};
struct Scale__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Scale__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Scale__1__VTable vtable;
};
struct NolanRankController__Array {
    struct NolanRankController__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct NolanRankController *vector[32];
};
struct NolanRankController__Array__VTable {
};
struct NolanRankController__Array__StaticFields {
};
struct NolanRankController__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct NolanRankController__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct NolanRankController__Array__VTable vtable;
};
struct Component__Array__Array {
    struct Component__Array__Array__Class *klass;
    MonitorData *monitor;
    Il2CppArrayBounds *bounds;
    il2cpp_array_size_t max_length;
    struct Component__Array *vector[32];
};
struct Component__Array__Array__VTable {
};
struct Component__Array__Array__StaticFields {
};
struct Component__Array__Array__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Component__Array__Array__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Component__Array__Array__VTable vtable;
};
struct Component__1__VTable {
};
struct Component__1__StaticFields {
};
struct Component__1__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct Component__1__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct Component__1__VTable vtable;
};
struct MethodInfo___VTable {
};
struct MethodInfo___StaticFields {
};
struct MethodInfo___Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct MethodInfo___StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct MethodInfo___VTable vtable;
};
struct BoltPrefabs {
    struct BoltPrefabs__Class *klass;
    MonitorData *monitor;
};
struct BoltPrefabs__VTable {
    VirtualInvokeData Equals;
    VirtualInvokeData Finalize;
    VirtualInvokeData GetHashCode;
    VirtualInvokeData ToString;
};
struct BoltPrefabs__StaticFields {
    struct PrefabId MapController;
    struct PrefabId Steam_Inventory_Validator;
    struct PrefabId Survival_Lobby_Controller;
    struct PrefabId DoorNumber;
    struct PrefabId SurvivalAltar;
    struct PrefabId SurvivalAltarMolly;
    struct PrefabId SurvivalAltarSlaughterhouse;
    struct PrefabId SurvivalAltarTown;
    struct PrefabId Cage;
    struct PrefabId TrashCan;
    struct PrefabId Key;
    struct PrefabId SurvivalBattery;
    struct PrefabId SurvivalBleach;
    struct PrefabId SurvivalBone;
    struct PrefabId SurvivalCake;
    struct PrefabId SurvivalCleanHead;
    struct PrefabId SurvivalEgg;
    struct PrefabId SurvivalFirstAid;
    struct PrefabId SurvivalFuse;
    struct PrefabId SurvivalGasoline;
    struct PrefabId SurvivalGoat;
    struct PrefabId SurvivalHay;
    struct PrefabId SurvivalHead;
    struct PrefabId SurvivalMatchbox;
    struct PrefabId SurvivalPig;
    struct PrefabId SurvivalRat;
    struct PrefabId SurvivalRitualBook;
    struct PrefabId SurvivalRose;
    struct PrefabId SurvivalRottenFood;
    struct PrefabId SurvivalSpade;
    struct PrefabId PetCat;
    struct PrefabId PetCrow;
    struct PrefabId PetGoat;
    struct PrefabId PetPig;
    struct PrefabId PetRat;
    struct PrefabId PetSpider;
    struct PrefabId SurvivalAzazelMolly;
    struct PrefabId SurvivalInmate;
    struct PrefabId SurvivalAnnaNew;
    struct PrefabId SurvivalDemon;
    struct PrefabId AzazelZara;
    struct PrefabId Spider;
    struct PrefabId AzazelApril;
    struct PrefabId Crow;
    struct PrefabId AzazelNathan;
    struct PrefabId Boar;
    struct PrefabId Corpse;
    struct PrefabId AzazelSam;
    struct PrefabId Ghost;
    struct PrefabId SurvivalAnna;
    struct PrefabId SurvivalApril;
    struct PrefabId SurvivalCultist;
    struct PrefabId SurvivalFrank;
    struct PrefabId SurvivalKai;
    struct PrefabId SurvivalMolly;
    struct PrefabId SurvivalNathan;
    struct PrefabId SurvivalSam;
    struct PrefabId SurvivalZara;
    struct PrefabId AsylumDoor;
    struct PrefabId AsylumDoubleDoor;
    struct PrefabId AsylumWhiteDoor;
    struct PrefabId Elevator_Door;
    struct PrefabId DevourDoorBack;
    struct PrefabId DevourDoorMain;
    struct PrefabId DevourDoorRoom;
    struct PrefabId InnDoor;
    struct PrefabId InnDoubleDoor;
    struct PrefabId InnShojiDoor;
    struct PrefabId InnShojiDoubleDoor;
    struct PrefabId InnWoodenDoor;
    struct PrefabId InnWoodenDoubleDoor;
    struct PrefabId ManorBarnDoor;
    struct PrefabId ManorDoor;
    struct PrefabId ManorGate;
    struct PrefabId ManorMausoleumDoor;
    struct PrefabId Animal_Gate;
    struct PrefabId SlaughterhouseFireEscapeDoor;
    struct PrefabId SlaughterhouseOfficeDoor;
    struct PrefabId SlaughterhousePushPullDoor;
    struct PrefabId TownCellDoor;
    struct PrefabId TownDoor;
    struct PrefabId TownDoor2;
    struct PrefabId TownLockbox;
    struct PrefabId InnFountain;
    struct PrefabId InnShrine;
    struct PrefabId InnWardrobe;
    struct PrefabId InnWeb;
    struct PrefabId TV;
    struct PrefabId ManorFountain;
    struct PrefabId ManorHidingSpot;
    struct PrefabId ManorLump;
    struct PrefabId ManorMirror;
    struct PrefabId ManorShrine;
    struct PrefabId Freezer_Room;
    struct PrefabId PigExcrement;
    struct PrefabId SurvivalBurstPipe;
    struct PrefabId Truck_Shutter;
    struct PrefabId SurvivalSmashableWindow;
    struct PrefabId SurvivalSmashableWindowMulti;
    struct PrefabId TownHidingSpot;
    struct PrefabId TownPentagram;
    struct PrefabId ManorIntro;
    struct PrefabId SlaughterhouseIntro;
    struct PrefabId TownIntro;
    struct PrefabId SurvivalMatchbox_02;
    struct PrefabId InnMaze_A;
    struct PrefabId InnMaze_B;
    struct PrefabId InnMaze_C;
    struct PrefabId InnMaze_D;
    struct PrefabId InnMaze_E;
    struct PrefabId InnMaze_F;
};
struct BoltPrefabs__Class {
    Il2CppClass_0 _0;
    Il2CppRuntimeInterfaceOffsetPair *interfaceOffsets;
    struct BoltPrefabs__StaticFields *static_fields;
    const Il2CppRGCTXData *rgctx_data;
    Il2CppClass_1 _1;
    struct BoltPrefabs__VTable vtable;
};
