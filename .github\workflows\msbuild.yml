# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: MSBuild

on:
  push:
    branches: [ "V2_recode" ]
  pull_request:
    branches: [ "V2_recode" ]

env:
  # Path to the solution file relative to the root of the project.
  SOLUTION_FILE_PATH: DevourClient.sln

  # Configuration type to build.
  # You can convert this to a build matrix if you need coverage of multiple configuration types.
  # https://docs.github.com/actions/learn-github-actions/managing-complex-workflows#using-a-build-matrix
  BUILD_CONFIGURATION: Release

permissions:
  contents: write

jobs:
  build:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4

    - name: Add MSBuild to PATH
      uses: microsoft/setup-msbuild@v1.0.2

    - name: Build
      working-directory: ${{env.GITHUB_WORKSPACE}}
      # Add additional options to the MSBuild command line here (like platform or verbosity level).
      # See https://docs.microsoft.com/visualstudio/msbuild/msbuild-command-line-reference
      run: msbuild /m /p:Configuration=${{env.BUILD_CONFIGURATION}} ${{env.SOLUTION_FILE_PATH}}

    - name: Create Release
      uses: ncipollo/release-action@v1
      with:
        tag: ${{ github.run_number }}
        name: Auto Release ${{ github.run_number }}
        artifacts: "x64/Release/DevourClient.dll"
        generateReleaseNotes: true
