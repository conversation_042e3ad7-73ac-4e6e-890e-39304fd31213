#!/usr/bin/env python3
"""
verify_il2cpp_types.py
----------------------
验证精简后的 il2cpp-types.h 文件是否包含了所有必要的类型定义。
检查是否有编译错误或缺失的类型定义。
"""

import os
import re
import sys
from pathlib import Path

def check_basic_types(file_path):
    """检查基础类型定义是否存在"""
    required_types = [
        'Il2CppClass',
        'Il2CppType', 
        'MethodInfo',
        'FieldInfo',
        'Il2CppObject',
        'Il2CppString',
        'Il2CppArray',
        'Il2CppAssembly',
        'Il2CppImage',
        'Il2CppDomain'
    ]
    
    missing_types = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        for type_name in required_types:
            # 检查是否有 typedef 或 struct 声明
            pattern = rf'(typedef\s+struct\s+{type_name}|struct\s+{type_name})'
            if not re.search(pattern, content):
                missing_types.append(type_name)
                
    except Exception as e:
        print(f"错误: 无法读取文件 {file_path}: {e}")
        return False
        
    if missing_types:
        print(f"警告: 以下基础类型定义缺失: {', '.join(missing_types)}")
        return False
    else:
        print("✓ 所有基础类型定义都存在")
        return True

def check_file_size(file_path):
    """检查文件大小是否合理"""
    try:
        size = os.path.getsize(file_path)
        size_kb = size / 1024
        
        print(f"文件大小: {size:,} 字节 ({size_kb:.1f} KB)")
        
        if size < 10000:  # 小于 10KB 可能太小
            print("警告: 文件可能过小，可能缺少重要定义")
            return False
        elif size > 1000000:  # 大于 1MB 可能没有精简
            print("警告: 文件可能过大，精简效果不明显")
            return False
        else:
            print("✓ 文件大小合理")
            return True
            
    except Exception as e:
        print(f"错误: 无法获取文件大小: {e}")
        return False

def check_header_integrity(file_path):
    """检查头文件的完整性"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 检查是否有基本的头文件结构
        checks = [
            (r'#define.*IS_DECOMPILER', "反编译器宏定义"),
            (r'typedef.*uint\d+_t', "基础整数类型"),
            (r'IL2CPP.*types', "IL2CPP 类型注释"),
            (r'typedef.*Il2Cpp', "IL2CPP 类型定义")
        ]
        
        all_passed = True
        for pattern, description in checks:
            if re.search(pattern, content, re.IGNORECASE):
                print(f"✓ {description} 存在")
            else:
                print(f"✗ {description} 缺失")
                all_passed = False
                
        return all_passed
        
    except Exception as e:
        print(f"错误: 无法检查头文件完整性: {e}")
        return False

def main():
    file_path = "appdata/il2cpp-types.h"
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
        
    print(f"验证文件: {file_path}")
    print("=" * 50)
    
    checks = [
        ("文件大小检查", lambda: check_file_size(file_path)),
        ("头文件完整性检查", lambda: check_header_integrity(file_path)),
        ("基础类型检查", lambda: check_basic_types(file_path))
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        if not check_func():
            all_passed = False
            
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ 所有检查通过！精简后的文件看起来正常。")
        sys.exit(0)
    else:
        print("✗ 某些检查失败，可能需要重新生成精简文件。")
        sys.exit(1)

if __name__ == "__main__":
    main()
