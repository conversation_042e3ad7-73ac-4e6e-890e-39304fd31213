#!/usr/bin/env python3
"""
create_slim_header.py
---------------------
创建一个轻量级的il2cpp-types头文件包装，避免直接引入巨大头文件带来的性能问题
"""
import argparse
import os
from pathlib import Path

def create_slim_header(input_path, output_path, common_lines=2000):
    """创建精简版头文件，保留必要的基础声明并使用条件包含原始头文件"""
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(input_path, 'r', encoding='utf-8', errors='ignore') as src:
        # 读取前N行（包含基础类型定义）
        header_lines = [next(src) for _ in range(common_lines) if src]
    
    # 重新组装轻量级头文件    
    with open(output_path, 'w', encoding='utf-8') as dst:
        dst.write("// 智能裁剪版 il2cpp-types.h - 性能优化版本\n\n")
        dst.write("#pragma once\n\n")
        
        # 添加预防重复包含的宏定义
        guard_macro = "_IL2CPP_TYPES_TRIMMED_H"
        dst.write(f"#ifndef {guard_macro}\n")
        dst.write(f"#define {guard_macro}\n\n")
        
        # 写入基础类型定义（前N行）
        dst.write("// ---------- 基础类型定义 ----------\n\n")
        dst.write(''.join(header_lines))
        dst.write("\n\n// ---------- 结束基础类型定义 ----------\n\n")
        
        # 添加预处理宏，在需要完整定义时包含原始文件
        dst.write("// 如果需要完整定义，请先定义 IL2CPP_TYPES_FULL_DEFINITION\n")
        dst.write("#ifdef IL2CPP_TYPES_FULL_DEFINITION\n")
        rel_path = os.path.relpath(input_path, os.path.dirname(output_path))
        dst.write(f'#include "{rel_path}"\n')
        dst.write("#endif // IL2CPP_TYPES_FULL_DEFINITION\n\n")
        
        dst.write("#endif // " + guard_macro + "\n")
    
    print(f"[已完成] 轻量级头文件已写入: {output_path}")
    print(f"提示: 在需要完整定义的文件中，包含此头文件前先定义 #define IL2CPP_TYPES_FULL_DEFINITION")

def main():
    parser = argparse.ArgumentParser(description="创建轻量级il2cpp-types.h包装头文件")
    parser.add_argument("--input", default="appdata/il2cpp-types.h", help="原始il2cpp-types.h路径")
    parser.add_argument("--output", default="appdata/il2cpp-types.trim.h", help="精简头文件输出路径")
    parser.add_argument("--common-lines", type=int, default=2000, help="保留的基础类型定义行数")
    
    args = parser.parse_args()
    create_slim_header(args.input, args.output, args.common_lines)

if __name__ == "__main__":
    main() 