<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="framework\dllmain.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="framework\helpers.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="framework\il2cpp-init.cpp" />
    <ClCompile Include="framework\pch-il2cpp.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="include\imgui\imgui.cpp" />
    <ClCompile Include="include\imgui\imgui_draw.cpp" />
    <ClCompile Include="include\imgui\imgui_impl_dx11.cpp" />
    <ClCompile Include="include\imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="include\imgui\imgui_stdlib.cpp" />
    <ClCompile Include="include\imgui\imgui_tables.cpp" />
    <ClCompile Include="include\imgui\imgui_widgets.cpp" />
    <ClCompile Include="include\Minhook\src\buffer.cpp" />
    <ClCompile Include="include\Minhook\src\HDE\hde32.cpp" />
    <ClCompile Include="include\Minhook\src\HDE\hde64.cpp" />
    <ClCompile Include="include\Minhook\src\hook.cpp" />
    <ClCompile Include="include\Minhook\src\trampoline.cpp" />
    <ClCompile Include="lib\private\ClientHelper.cpp" />
    <ClCompile Include="lib\private\UnityEngine\Camera.cpp" />
    <ClCompile Include="lib\private\UnityEngine\GameObject.cpp" />
    <ClCompile Include="lib\private\UnityEngine\Input.cpp" />
    <ClCompile Include="lib\private\UnityEngine\Math.cpp" />
    <ClCompile Include="lib\private\UnityEngine\Object.cpp" />
    <ClCompile Include="lib\private\UnityEngine\Transform.cpp" />
    <ClCompile Include="lib\private\wrapper.cpp" />
    <ClCompile Include="user\features\esp\esp.cpp" />
    <ClCompile Include="user\features\menu.cpp" />
    <ClCompile Include="user\features\misc\misc.cpp" />
    <ClCompile Include="user\hooks\hooks.cpp" />
    <ClCompile Include="user\main.cpp" />
    <ClCompile Include="user\network\VersionControl.cpp" />
    <ClCompile Include="user\players\players.cpp" />
    <ClCompile Include="user\settings\settings.cpp" />
    <ClCompile Include="user\utils\utils.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="appdata\il2cpp-api-functions.h" />
    <ClInclude Include="appdata\il2cpp-api-functions-ptr.h" />
    <ClInclude Include="appdata\il2cpp-functions.h" />
    <ClInclude Include="appdata\il2cpp-metadata-version.h" />
    <ClInclude Include="appdata\il2cpp-types-ptr.h" />
    <ClInclude Include="appdata\il2cpp-types.h" />
    <ClInclude Include="appdata\il2cpp-types.trim.h" />
    <ClInclude Include="framework\helpers.h" />
    <ClInclude Include="framework\il2cpp-appdata.h" />
    <ClInclude Include="framework\il2cpp-init.h" />
    <ClInclude Include="framework\pch-il2cpp.h" />
    <ClInclude Include="include\color.hpp" />
    <ClInclude Include="include\imgui\imconfig.h" />
    <ClInclude Include="include\imgui\imgui.h" />
    <ClInclude Include="include\imgui\imgui_impl_dx11.h" />
    <ClInclude Include="include\imgui\imgui_impl_win32.h" />
    <ClInclude Include="include\imgui\imgui_internal.h" />
    <ClInclude Include="include\imgui\imgui_stdlib.h" />
    <ClInclude Include="include\imgui\imstb_rectpack.h" />
    <ClInclude Include="include\imgui\imstb_textedit.h" />
    <ClInclude Include="include\imgui\imstb_truetype.h" />
    <ClInclude Include="include\json.hpp" />
    <ClInclude Include="include\magic_enum.hpp" />
    <ClInclude Include="include\Minhook\include\MinHook.h" />
    <ClInclude Include="include\Minhook\src\buffer.h" />
    <ClInclude Include="include\Minhook\src\HDE\hde32.h" />
    <ClInclude Include="include\Minhook\src\HDE\hde64.h" />
    <ClInclude Include="include\Minhook\src\HDE\pstdint.h" />
    <ClInclude Include="include\Minhook\src\HDE\table32.h" />
    <ClInclude Include="include\Minhook\src\HDE\table64.h" />
    <ClInclude Include="include\Minhook\src\trampoline.h" />
    <ClInclude Include="lib\public\ClientHelper.h" />
    <ClInclude Include="lib\public\UnityEngine\Camera.h" />
    <ClInclude Include="lib\public\UnityEngine\Engine.hpp" />
    <ClInclude Include="lib\public\UnityEngine\GameObject.h" />
    <ClInclude Include="lib\public\UnityEngine\Input.h" />
    <ClInclude Include="lib\public\UnityEngine\Math.h" />
    <ClInclude Include="lib\public\UnityEngine\Object.h" />
    <ClInclude Include="lib\public\UnityEngine\Transform.h" />
    <ClInclude Include="lib\public\wrapper.h" />
    <ClInclude Include="user\features\esp\esp.hpp" />
    <ClInclude Include="user\features\menu.hpp" />
    <ClInclude Include="user\features\misc\misc.h" />
    <ClInclude Include="user\hooks\hooks.hpp" />
    <ClInclude Include="user\main.h" />
    <ClInclude Include="user\network\VersionControl.h" />
    <ClInclude Include="user\players\players.h" />
    <ClInclude Include="user\settings\settings.hpp" />
    <ClInclude Include="user\utils\utils.hpp" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <!--<VCProjectVersion>16.0</VCProjectVersion>-->
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{AFA414F5-355A-472E-822F-244F167E5B0D}</ProjectGuid>
    <RootNamespace>Il2CppDLL</RootNamespace>
    <ProjectName>DevourClient</ProjectName>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <!--<WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>-->
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <!--<PlatformToolset>v142</PlatformToolset>-->
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <!--<PlatformToolset>v142</PlatformToolset>-->
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <!--<PlatformToolset>v142</PlatformToolset>-->
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <!--<PlatformToolset>v142</PlatformToolset>-->
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(SolutionDir)include\;$(SolutionDir)include\Minhook\include;C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Include;$(IncludePath)</IncludePath>
    <LibraryPath>C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Lib\x64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(SolutionDir)include\;$(SolutionDir)include\Minhook\include;C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Include;$(IncludePath)</IncludePath>
    <LibraryPath>C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Lib\x64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;IL2CPPDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch-il2cpp.h</PrecompiledHeaderFile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user;$(ProjectDir)lib</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;IL2CPPDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch-il2cpp.h</PrecompiledHeaderFile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;IL2CPPDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions);_SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch-il2cpp.h</PrecompiledHeaderFile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user;$(ProjectDir)lib\public;</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;IL2CPPDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions);_SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch-il2cpp.h</PrecompiledHeaderFile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user;$(ProjectDir)lib\public</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>