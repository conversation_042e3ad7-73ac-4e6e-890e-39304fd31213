# IL2CPP Types 头文件优化说明

## 优化效果

`il2cpp-types.h` 文件已经通过智能精简脚本进行了大幅优化：

- **原始文件大小**: 64,579,165 字节 (约 64.6 MB)
- **精简后文件大小**: 80,376 字节 (约 80.4 KB)
- **精简比例**: 99.88% 的未使用内容被移除
- **保留类型数量**: 1 个实际使用的类型

## 优化原理

精简脚本 `scripts/prune_il2cpp_types.py` 的工作原理：

1. **扫描源代码**: 遍历项目中的所有 C/C++ 源文件
2. **识别使用的类型**: 通过正则表达式查找所有 `XXX__TypeInfo` 引用
3. **保留必要定义**: 只保留实际使用的类型的结构体定义和声明
4. **保留基础类型**: 保留 IL2CPP 运行时必需的基础类型定义

## 文件备份

- `il2cpp-types.original.h` - 原始的完整文件备份
- `il2cpp-types.trimmed.h` - 精简版本
- `il2cpp-types.h` - 当前使用的精简版本

## 重新生成精简版本

如果项目中添加了新的 IL2CPP 类型使用，可以重新运行精简脚本：

```bash
python scripts/prune_il2cpp_types.py --input appdata/il2cpp-types.original.h --output appdata/il2cpp-types.h
```

## 性能提升

这种优化带来的好处：

1. **编译速度**: 大幅减少编译时间
2. **IntelliSense 性能**: 显著提升 IDE 的代码补全和分析速度
3. **内存使用**: 减少编译器和 IDE 的内存占用
4. **项目加载**: 加快项目打开和索引速度

## 注意事项

- 如果编译时出现类型未定义错误，说明有新的类型被使用但未包含在精简版本中
- 此时需要重新运行精简脚本或手动添加缺失的类型定义
- 建议在添加新的 IL2CPP 功能后重新运行精简脚本

## 相关脚本

- `scripts/prune_il2cpp_types.py` - 主要的精简脚本
- `scripts/create_slim_header.py` - 创建轻量级包装头文件
- `scripts/create_complete_header.py` - 创建完整的优化版头文件
